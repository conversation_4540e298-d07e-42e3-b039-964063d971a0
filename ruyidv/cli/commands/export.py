"""
Data export commands
"""

from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

from ruyidv.database import SessionLocal
from ruyidv.models import SampleMode
from ruyidv.services import BatchService, ExportService

console = Console()


@click.group()
def export():
    """Data export commands"""
    pass


@export.command()
@click.option(
    "--output",
    "-o",
    required=True,
    type=click.Path(path_type=Path),
    help="Output file path (for single file) or directory path (for dataset splitting)",
)
@click.option(
    "--format",
    "export_format",
    default="sharegpt",
    type=click.Choice(["sharegpt"], case_sensitive=False),
    help="Export format (default: sharegpt)",
)
@click.option(
    "--batch-id", "-b", help="Filter by batch IDs (comma-separated, e.g., '1,2,3')"
)
@click.option(
    "--mode",
    "-m",
    help="Filter by sample modes (comma-separated, e.g., 'grounding,describe')",
)
@click.option("--labels", "-l", help="Filter by labels (comma-separated)")
@click.option(
    "--formatter",
    "-f",
    help="Use custom formatter function (default: built-in formatter for each mode)",
)
@click.option(
    "--limit",
    default=None,
    type=int,
    help="Maximum number of samples to export (default: all)",
)
@click.option("--offset", default=0, help="Number of samples to skip (default: 0)")
@click.option(
    "--copy-images",
    is_flag=True,
    help="Copy image files to export directory instead of using absolute paths",
)
@click.option(
    "--image-dir",
    default="images",
    help="Image subdirectory name when using --copy-images (default: images)",
)
@click.option(
    "--split-ratio",
    default="8,1,1",
    help="Dataset split ratios for train,val,test (e.g., '8,1,1' for 8:1:1 ratio)",
)
@click.option(
    "--random-seed",
    type=int,
    help="Random seed for reproducible dataset splitting",
)
@click.option(
    "--batch-size",
    type=int,
    help="Training batch size - ensures train/val/test sets have sample counts that are multiples of this value",
)
@click.option(
    "--force-mapreduce",
    is_flag=True,
    help="Force use of Map-Reduce pipeline regardless of dataset size",
)
@click.option(
    "--chunk-size",
    type=int,
    help="Chunk size for Map-Reduce processing (default: from config)",
)
def data(
    output: Path,
    export_format: str,
    batch_id: Optional[str] = None,
    mode: Optional[str] = None,
    labels: Optional[str] = None,
    formatter: Optional[str] = None,
    limit: Optional[int] = None,
    offset: int = 0,
    copy_images: bool = False,
    image_dir: str = "images",
    split_ratio: str = "8,1,1",
    random_seed: Optional[int] = None,
    batch_size: Optional[int] = None,
    force_mapreduce: bool = False,
    chunk_size: Optional[int] = None,
):
    """Export training data in specified format"""
    db = SessionLocal()
    try:
        export_service = ExportService(db)

        console.print("[blue]📤 Preparing to export data...[/blue]")

        # 验证批次大小参数
        if batch_size is not None:
            if batch_size <= 0:
                console.print("[red]❌ Batch size must be a positive integer[/red]")
                raise click.Abort()
            console.print(f"[blue]🎯 Batch size constraint: {batch_size}[/blue]")

        # 批次分层抽样是默认行为
        console.print(
            "[blue]📊 Batch stratification enabled by default (proportional sampling)[/blue]"
        )

        # 处理过滤参数
        filters = {}
        batch_ids = None
        sample_modes = None

        if batch_id:
            try:
                batch_ids = [int(id.strip()) for id in batch_id.split(",")]
                filters["batch_ids"] = batch_ids
                console.print(
                    f"[blue]📦 Batches: {', '.join(map(str, batch_ids))}[/blue]"
                )
            except ValueError:
                console.print(f"[red]❌ Invalid batch IDs: {batch_id}[/red]")
                raise click.Abort()

        if mode:
            try:
                sample_modes = [SampleMode(m.strip()) for m in mode.split(",")]
                filters["modes"] = sample_modes
                console.print(
                    f"[blue]🎯 Modes: {', '.join(m.value for m in sample_modes)}[/blue]"
                )
            except ValueError as e:
                console.print(f"[red]❌ Invalid mode: {e}[/red]")
                raise click.Abort()

        label_list = None
        if labels:
            label_list = [label.strip() for label in labels.split(",")]
            filters["labels"] = label_list
            console.print(f"[blue]🏷️ Labels: {', '.join(label_list)}[/blue]")

        # 判断是否使用数据集划分：如果output是目录则启用划分
        use_split = output.is_dir() or (not output.exists() and not output.suffix)
        split_ratios = [8, 1, 1]  # 默认值

        if use_split:
            try:
                split_ratios = [int(r.strip()) for r in split_ratio.split(",")]
                if len(split_ratios) != 3:
                    raise ValueError("Split ratio must have exactly 3 values")
                if any(r <= 0 for r in split_ratios):
                    raise ValueError("All split ratios must be positive")
            except ValueError as e:
                console.print(f"[red]❌ Invalid split ratio '{split_ratio}': {e}[/red]")
                raise click.Abort()

        # 显示配置信息
        console.print("\n[bold]Export Configuration:[/bold]")
        console.print(f"[bold]Format:[/bold] {export_format}")
        if use_split:
            console.print(f"[bold]Output Directory:[/bold] {output}")
            console.print(
                f"[bold]Split Ratio:[/bold] {':'.join(map(str, split_ratios))} (train:val:test)"
            )
            if random_seed is not None:
                console.print(f"[bold]Random Seed:[/bold] {random_seed}")
        else:
            console.print(f"[bold]Output:[/bold] {output}")
        console.print(
            f"[bold]Formatter:[/bold] {formatter if formatter else 'Built-in default formatters'}"
        )
        console.print(
            f"[bold]Limit:[/bold] {limit if limit is not None else 'All'} samples"
        )
        if offset > 0:
            console.print(f"[bold]Offset:[/bold] {offset} samples")

        # 显示图片复制配置
        if copy_images:
            console.print("[bold]Copy Images:[/bold] Yes")
            console.print(f"[bold]Image Directory:[/bold] {image_dir}")
            # 确保输出目录存在
            if use_split:
                output.mkdir(parents=True, exist_ok=True)
            else:
                output.parent.mkdir(parents=True, exist_ok=True)
        else:
            console.print("[bold]Copy Images:[/bold] No (using absolute paths)")

        # 初始化变量以避免unbound错误
        split_data = {}
        exported_data = []
        saved_path = None
        export_errors = []

        # 执行导出
        if use_split:
            # 数据集划分导出，选择导出方法
            from ruyidv.config import settings

            # 获取样本总数以决定使用哪种导出方法
            sample_count = export_service.sample_service.count_samples_with_filters(
                batch_ids=batch_ids, modes=sample_modes, labels=label_list
            )

            # 确定实际导出数量
            actual_export_count = (
                min(sample_count - offset, limit)
                if limit is not None
                else (sample_count - offset)
            )

            # 决定是否使用Map-Reduce
            use_mapreduce_split = (
                force_mapreduce
                or actual_export_count > settings.EXPORT_MAPREDUCE_THRESHOLD
            )

            # 确定块大小
            effective_chunk_size = (
                chunk_size
                if chunk_size is not None
                else settings.EXPORT_MAPREDUCE_CHUNK_SIZE
            )

            if use_mapreduce_split:
                if force_mapreduce:
                    console.print(
                        f"[blue]🚀 Map-Reduce split mode forced by user ({actual_export_count} samples)[/blue]"
                    )
                else:
                    console.print(
                        f"[blue]🚀 Large dataset splitting detected ({actual_export_count} samples). "
                        f"Using Map-Reduce pipeline for optimal performance[/blue]"
                    )
                console.print(
                    f"[blue]📦 Processing in chunks of {effective_chunk_size} samples[/blue]"
                )

                # 使用并行数据集划分导出
                from ruyidv.services.parallel_export_service import (
                    ParallelExportService,
                )

                parallel_export_service = ParallelExportService(db)

                split_data, export_errors = (
                    parallel_export_service.export_with_split_parallel(
                        batch_ids=batch_ids,
                        modes=sample_modes,
                        labels=label_list,
                        formatter=formatter,
                        limit=limit,
                        offset=offset,
                        copy_images=copy_images,
                        export_dir=output if copy_images else None,
                        image_subdir=image_dir,
                        split_ratios=split_ratios,
                        random_seed=random_seed,
                        chunk_size=effective_chunk_size,
                        console=console,
                    )
                )
            else:
                # 使用传统的数据集划分导出
                split_data, export_errors = export_service.export_with_split(
                    batch_ids=batch_ids,
                    modes=sample_modes,
                    labels=label_list,
                    formatter=formatter,
                    limit=limit,
                    offset=offset,
                    copy_images=copy_images,
                    export_dir=output if copy_images else None,
                    image_subdir=image_dir,
                    split_ratios=split_ratios,
                    random_seed=random_seed,
                    batch_size=batch_size,
                    console=console,
                )
        else:
            # 单文件导出，选择导出方法
            from ruyidv.config import settings

            # 获取样本总数以决定使用哪种导出方法
            sample_count = export_service.sample_service.count_samples_with_filters(
                batch_ids=batch_ids, modes=sample_modes, labels=label_list
            )

            # 确定实际导出数量
            actual_export_count = (
                min(sample_count - offset, limit)
                if limit is not None
                else (sample_count - offset)
            )

            # 决定是否使用Map-Reduce
            use_mapreduce = (
                force_mapreduce
                or actual_export_count > settings.EXPORT_MAPREDUCE_THRESHOLD
            )

            # 确定块大小
            effective_chunk_size = (
                chunk_size
                if chunk_size is not None
                else settings.EXPORT_MAPREDUCE_CHUNK_SIZE
            )

            if use_mapreduce:
                if force_mapreduce:
                    console.print(
                        f"[blue]🚀 Map-Reduce export mode forced by user ({actual_export_count} samples)[/blue]"
                    )
                else:
                    console.print(
                        f"[blue]🚀 Large dataset detected ({actual_export_count} samples). "
                        f"Using Map-Reduce pipeline for optimal performance[/blue]"
                    )
                console.print(
                    f"[blue]📦 Processing in chunks of {effective_chunk_size} samples[/blue]"
                )

                # 使用并行导出
                from ruyidv.services.parallel_export_service import (
                    ParallelExportService,
                )

                parallel_export_service = ParallelExportService(db)

                exported_data, export_errors = parallel_export_service.export_parallel(
                    batch_ids=batch_ids,
                    modes=sample_modes,
                    labels=label_list,
                    formatter=formatter,
                    limit=limit,
                    offset=offset,
                    copy_images=copy_images,
                    export_dir=output.parent if copy_images else None,
                    image_subdir=image_dir,
                    chunk_size=effective_chunk_size,
                    console=console,
                )
            else:
                # 使用传统的批次分层抽样导出
                exported_data, export_errors = (
                    export_service.export_with_filters_and_stratification(
                        batch_ids=batch_ids,
                        modes=sample_modes,
                        labels=label_list,
                        formatter=formatter,
                        limit=limit,
                        offset=offset,
                        copy_images=copy_images,
                        export_dir=output.parent if copy_images else None,
                        image_subdir=image_dir,
                        random_seed=random_seed,
                        console=console,
                    )
                )

        if use_split:
            # 处理数据集划分的结果
            if not any(split_data.values()):
                console.print(
                    "\n[yellow]⚠️ No data found matching the specified filters[/yellow]"
                )
                if export_errors:
                    console.print("[red]Errors encountered during export:[/red]")
                    for error in export_errors[:5]:  # 只显示前5个错误
                        console.print(f"  [red]• {error}[/red]")
                    if len(export_errors) > 5:
                        console.print(
                            f"  [red]... and {len(export_errors) - 5} more errors[/red]"
                        )
                return

            # 保存分割的数据集文件
            console.print("[blue]💾 Saving split dataset files...[/blue]")
            saved_paths = {}
            for split_name, data in split_data.items():
                if data:  # 只保存非空的数据集
                    filename = f"{split_name}.json"
                    saved_path = export_service.save_export_file(data, filename, output)
                    saved_paths[split_name] = saved_path
        else:
            # 处理单文件导出的结果
            if not exported_data:
                console.print(
                    "\n[yellow]⚠️ No data found matching the specified filters[/yellow]"
                )
                if export_errors:
                    console.print("[red]Errors encountered during export:[/red]")
                    for error in export_errors[:5]:  # 只显示前5个错误
                        console.print(f"  [red]• {error}[/red]")
                    if len(export_errors) > 5:
                        console.print(
                            f"  [red]... and {len(export_errors) - 5} more errors[/red]"
                        )
                return

            # 保存文件
            console.print("[blue]💾 Saving export file...[/blue]")
            saved_path = export_service.save_export_file(
                exported_data, output.name, output.parent
            )

        # 显示导出结果
        if use_split:
            # 显示数据集划分结果
            total_items = sum(len(data) for data in split_data.values())

            result_message = "[green]✓[/green] Dataset split export completed!\n\n"
            result_message += f"[bold]Output Directory:[/bold] {output}\n"
            result_message += f"[bold]Total Items:[/bold] {total_items}\n"

            for split_name, data in split_data.items():
                if data:
                    result_message += f"[bold]{split_name.capitalize()} Set:[/bold] {len(data)} samples\n"

            result_message += "[bold]Format:[/bold] SHAREGPT"

            if copy_images:
                result_message += (
                    f"\n[bold]Images Copied:[/bold] Yes (to {image_dir}/ directory)"
                )

            if export_errors:
                result_message += (
                    f"\n[bold]Errors:[/bold] {len(export_errors)} encountered"
                )

            console.print(
                Panel.fit(
                    result_message,
                    title="🎉 Dataset Split Complete",
                    border_style="green" if not export_errors else "yellow",
                )
            )
        else:
            # 显示单文件导出结果
            stats = export_service.get_export_statistics(exported_data)

            # 构建结果消息
            result_message = (
                f"[green]✓[/green] Export completed!\n\n"
                f"[bold]Output File:[/bold] {saved_path}\n"
                f"[bold]Total Items:[/bold] {stats['total_items']}\n"
                f"[bold]Unique Images:[/bold] {stats['unique_images']}\n"
                f"[bold]Format:[/bold] {stats['export_format'].upper()}"
            )

            if copy_images:
                result_message += (
                    f"\n[bold]Images Copied:[/bold] Yes (to {image_dir}/ directory)"
                )

            if export_errors:
                result_message += (
                    f"\n[bold]Errors:[/bold] {len(export_errors)} encountered"
                )

            console.print(
                Panel.fit(
                    result_message,
                    title="🎉 Export Complete",
                    border_style="green" if not export_errors else "yellow",
                )
            )

        # 显示错误详情
        if export_errors:
            console.print("\n[yellow]⚠️ Errors encountered during export:[/yellow]")
            for i, error in enumerate(export_errors[:10]):  # 显示前10个错误
                console.print(f"  [red]{i + 1}. {error}[/red]")
            if len(export_errors) > 10:
                console.print(
                    f"  [red]... and {len(export_errors) - 10} more errors[/red]"
                )

        # 显示样本预览
        if use_split:
            # 显示数据集划分的样本预览
            for split_name, data in split_data.items():
                if data:
                    console.print(
                        f"\n[bold]{split_name.capitalize()} Set Preview:[/bold]"
                    )
                    preview_count = min(2, len(data))

                    for i, item in enumerate(data[:preview_count]):
                        console.print(
                            f"\n[cyan]{split_name.capitalize()} Sample {i + 1}:[/cyan]"
                        )
                        console.print(
                            f"  [bold]Instruction:[/bold] {item['instruction'][:80]}{'...' if len(item['instruction']) > 80 else ''}"
                        )
                        console.print(
                            f"  [bold]Output:[/bold] {item['output'][:80]}{'...' if len(item['output']) > 80 else ''}"
                        )
                        console.print(
                            f"  [bold]Images:[/bold] {len(item['images'])} image(s)"
                        )

                    if len(data) > preview_count:
                        console.print(
                            f"[dim]... and {len(data) - preview_count} more {split_name} samples[/dim]"
                        )
        else:
            # 显示单文件导出的样本预览
            if exported_data:
                console.print("\n[bold]Sample Preview:[/bold]")
                preview_count = min(3, len(exported_data))

                for i, item in enumerate(exported_data[:preview_count]):
                    console.print(f"\n[cyan]Sample {i + 1}:[/cyan]")
                    console.print(
                        f"  [bold]Instruction:[/bold] {item['instruction'][:100]}{'...' if len(item['instruction']) > 100 else ''}"
                    )
                    console.print(
                        f"  [bold]Output:[/bold] {item['output'][:100]}{'...' if len(item['output']) > 100 else ''}"
                    )
                    console.print(
                        f"  [bold]Images:[/bold] {len(item['images'])} image(s)"
                    )

                if len(exported_data) > preview_count:
                    console.print(
                        f"\n[dim]... and {len(exported_data) - preview_count} more samples[/dim]"
                    )

        # 显示过滤器摘要
        if filters or use_split:
            filter_table = Table(show_header=True, header_style="bold magenta")
            filter_table.add_column("Filter", style="bold")
            filter_table.add_column("Value")

            for key, value in filters.items():
                if key == "modes":
                    filter_table.add_row("Modes", ", ".join(m.value for m in value))
                elif key == "labels":
                    filter_table.add_row("Labels", ", ".join(value))
                else:
                    filter_table.add_row(key.replace("_", " ").title(), str(value))

            if use_split:
                filter_table.add_row(
                    "Dataset Split",
                    f"{':'.join(map(str, split_ratios))} (train:val:test)",
                )
                if random_seed is not None:
                    filter_table.add_row("Random Seed", str(random_seed))

            console.print(
                Panel(
                    filter_table,
                    title="🔍 Applied Filters & Settings",
                    border_style="blue",
                )
            )

    except Exception as e:
        console.print(f"[red]❌ Unexpected error during export: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@export.command()
@click.option(
    "--batch-id", "-b", type=int, help="Show export potential for specific batch"
)
@click.option(
    "--mode",
    "-m",
    type=click.Choice([mode.value for mode in SampleMode], case_sensitive=False),
    help="Show export potential for specific mode",
)
def info(batch_id: Optional[int] = None, mode: Optional[str] = None):
    """Show export information and statistics"""
    db = SessionLocal()
    try:
        batch_service = BatchService(db)

        console.print("[bold]📊 Export Information[/bold]\n")

        # 如果指定了批次，显示批次详情
        if batch_id:
            batch = batch_service.get_batch_by_id(batch_id)
            if not batch:
                console.print(f"[red]❌ Batch with ID {batch_id} not found[/red]")
                raise click.Abort()

            stats = batch_service.get_batch_statistics(batch_id)

            if not stats:
                console.print(
                    f"[red]❌ Unable to get statistics for batch {batch_id}[/red]"
                )
                raise click.Abort()

            batch_info = Table(show_header=True, header_style="bold cyan")
            batch_info.add_column("Metric", style="bold")
            batch_info.add_column("Value", justify="right")

            batch_info.add_row("Batch Name", batch.name)
            batch_info.add_row("Total Samples", str(stats["total_samples"]))
            batch_info.add_row("Unique Images", str(stats["unique_images"]))
            batch_info.add_row("Unique Labels", str(len(stats["all_labels"])))

            console.print(
                Panel(
                    batch_info,
                    title=f"📦 Batch {batch_id} Export Potential",
                    border_style="blue",
                )
            )

            # 模式分布
            if stats["mode_distribution"]:
                mode_table = Table(show_header=True, header_style="bold yellow")
                mode_table.add_column("Mode", style="bold")
                mode_table.add_column("Samples", justify="right")
                mode_table.add_column("Export Ready", justify="center")

                for mode_name, count in stats["mode_distribution"].items():
                    mode_table.add_row(
                        mode_name.replace("_", " ").title(), str(count), "✅"
                    )

                console.print(
                    Panel(
                        mode_table,
                        title="📊 Mode Distribution",
                        border_style="yellow",
                    )
                )

            # 显示标签
            if stats["all_labels"]:
                labels_preview = ", ".join(list(stats["all_labels"])[:10])
                if len(stats["all_labels"]) > 10:
                    labels_preview += f" ... (+{len(stats['all_labels']) - 10} more)"

                console.print(f"\n[bold]Available Labels:[/bold] {labels_preview}")

        else:
            # 显示总体导出统计
            all_batches = batch_service.get_all_batches()

            if not all_batches:
                console.print("[yellow]No batches found for export[/yellow]")
                return

            # 总体统计
            total_samples = 0
            total_images = 0
            for batch in all_batches:
                batch_stats = batch_service.get_batch_statistics(batch.id)
                if batch_stats:
                    total_samples += batch_stats["total_samples"]
                    total_images += batch_stats["unique_images"]

            overview_table = Table(show_header=True, header_style="bold green")
            overview_table.add_column("Metric", style="bold")
            overview_table.add_column("Value", justify="right")

            overview_table.add_row("Total Batches", str(len(all_batches)))
            overview_table.add_row("Total Samples", str(total_samples))
            overview_table.add_row("Total Images", str(total_images))

            console.print(
                Panel(
                    overview_table,
                    title="🌍 System Export Overview",
                    border_style="green",
                )
            )

    except Exception as e:
        console.print(f"[red]❌ Failed to get export info: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@export.command(name="list-formatters")
def list_formatters_cmd():
    """List all available custom formatters"""
    from ruyidv.services import get_formatter_info, list_formatters

    console.print("[bold]📋 Available Custom Formatters[/bold]\n")

    formatters = list_formatters()

    if not formatters:
        console.print("[yellow]No custom formatters registered[/yellow]")
        console.print("\n[dim]You can register formatters using:[/dim]")
        console.print("[dim]  from ruyidv.services import register_formatter[/dim]")
        console.print("[dim]  register_formatter('my_formatter', my_function)[/dim]")
        console.print("\n[dim]Or create formatter files in ~/.ruyidv/formatters/[/dim]")
        console.print("[dim]  ruyidv export create-example[/dim]")
        return

    # 创建格式化器信息表
    formatter_table = Table(show_header=True, header_style="bold cyan")
    formatter_table.add_column("Name", style="bold")
    formatter_table.add_column("Description", style="dim")
    formatter_table.add_column("Source", style="blue", width=10)
    formatter_table.add_column("Usage", style="green")

    for name in formatters:
        info = get_formatter_info(name)
        if info:
            description = info["description"].split("\n")[0].strip()
            source = info["source"]

            formatter_table.add_row(
                name,
                description[:50] + "..." if len(description) > 50 else description,
                source,
                f"--formatter {name}",
            )

    console.print(
        Panel(formatter_table, title="🎯 Custom Formatters", border_style="blue")
    )

    # 显示使用示例
    if formatters:
        console.print("\n[bold]Usage Examples:[/bold]")
        console.print("  [dim]# Use a custom formatter[/dim]")
        console.print(
            f"  ruyidv export data -o output.json --formatter {formatters[0]}"
        )
        console.print("  [dim]# Use with filters[/dim]")
        console.print(
            f"  ruyidv export data -o output.json --formatter {formatters[0]} --mode grounding --batch-id 1,2"
        )


@export.command(name="create-example")
def create_example_formatters():
    """Create example formatter file in user directory"""
    from ruyidv.config import settings
    from ruyidv.services import create_example_formatter_file

    console.print("[blue]📝 Creating example formatter file...[/blue]")

    success = create_example_formatter_file()

    if success:
        example_file = settings.USER_FORMATTERS_DIR / "example_formatters.py"
        console.print(
            Panel.fit(
                f"[green]✅ Example formatter file created![/green]\n\n"
                f"[bold]Location:[/bold] {example_file}\n"
                f"[bold]Directory:[/bold] {settings.USER_FORMATTERS_DIR}\n\n"
                f"[dim]Edit the file to customize your formatters.[/dim]\n"
                f"[dim]Restart the application to load new formatters.[/dim]",
                title="📝 Example Created",
                border_style="green",
            )
        )

        console.print("\n[bold]Next Steps:[/bold]")
        console.print("1. Edit the example file to create your custom formatters")
        console.print("2. Make sure function names contain 'formatter'")
        console.print("3. Restart the application or run: ruyidv export reload")
        console.print(
            "4. Use: ruyidv export list-formatters to see your new formatters"
        )
    else:
        console.print(
            f"[yellow]⚠️ Example file already exists at:[/yellow]\n"
            f"  {settings.USER_FORMATTERS_DIR / 'example_formatters.py'}\n\n"
            f"[dim]Delete the existing file if you want to recreate it.[/dim]"
        )


@export.command(name="reload")
def reload_formatters():
    """Reload user formatters from directory"""
    from ruyidv.services import list_formatters, load_user_formatters

    console.print("[blue]🔄 Reloading user formatters...[/blue]")

    # 获取重新加载前的格式化器数量
    old_formatters = set(list_formatters())

    # 重新加载
    try:
        load_user_formatters()
        new_formatters = set(list_formatters())

        # 计算变化
        added = new_formatters - old_formatters

        if added:
            console.print(
                Panel.fit(
                    f"[green]✅ Formatters reloaded successfully![/green]\n\n"
                    f"[bold]New formatters:[/bold] {', '.join(added)}\n"
                    f"[bold]Total formatters:[/bold] {len(new_formatters)}",
                    title="🔄 Reload Complete",
                    border_style="green",
                )
            )
        else:
            console.print(
                f"[yellow]No new formatters found.[/yellow]\n"
                f"Total formatters: {len(new_formatters)}"
            )

    except Exception as e:
        console.print(f"[red]❌ Failed to reload formatters: {e}[/red]")


@export.command(name="formatter-info")
@click.argument("formatter_name")
def formatter_info(formatter_name: str):
    """Show detailed information about a specific formatter"""
    from ruyidv.services import get_formatter_info

    info = get_formatter_info(formatter_name)

    if not info:
        console.print(f"[red]❌ Formatter '{formatter_name}' not found[/red]")

        from ruyidv.services import list_formatters

        available = list_formatters()
        if available:
            console.print(f"\n[dim]Available formatters: {', '.join(available)}[/dim]")
        return

    # 创建详细信息表
    info_table = Table(show_header=True, header_style="bold cyan")
    info_table.add_column("Property", style="bold")
    info_table.add_column("Value")

    info_table.add_row("Name", info["name"])
    info_table.add_row("Source", info["source"])
    info_table.add_row("Module", info["module"])

    console.print(
        Panel(info_table, title=f"🔍 Formatter: {formatter_name}", border_style="blue")
    )

    # 显示文档
    if info["description"]:
        console.print("\n[bold]Description:[/bold]")
        console.print(Panel(info["description"], border_style="dim"))

    # 显示使用示例
    console.print("\n[bold]Usage Examples:[/bold]")
    console.print(f"  ruyidv export data -o output.json --formatter {formatter_name}")
    console.print(
        f"  ruyidv export data -o output.json --formatter {formatter_name} --mode grounding"
    )


@export.command(name="clear-cache")
def clear_formatter_cache():
    """Clear formatter validation cache"""
    from ruyidv.services import get_cache_file_path

    cache_file = get_cache_file_path()

    if cache_file.exists():
        try:
            cache_file.unlink()
            console.print(
                Panel.fit(
                    "[green]✅ Formatter validation cache cleared successfully![/green]\n\n"
                    "[dim]All formatters will be re-validated on next load.[/dim]",
                    title="🗑️ Cache Cleared",
                    border_style="green",
                )
            )
        except Exception as e:
            console.print(f"[red]❌ Failed to clear cache: {e}[/red]")
    else:
        console.print("[yellow]No cache file found.[/yellow]")
