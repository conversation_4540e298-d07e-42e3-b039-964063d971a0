"""
Data query commands
"""

from typing import Optional

import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

from ruyidv.database import SessionLocal
from ruyidv.models import SampleMode
from ruyidv.services import BatchService, ImageService, SampleService

console = Console()


@click.group()
def query():
    """Data query commands"""
    pass


@query.command()
@click.option("--batch-id", "-b", type=int, help="Filter by batch ID")
@click.option(
    "--mode",
    "-m",
    type=click.Choice([mode.value for mode in SampleMode], case_sensitive=False),
    help="Filter by sample mode",
)
@click.option("--labels", "-l", help="Filter by labels (comma-separated)")
@click.option(
    "--limit", default=50, help="Maximum number of samples to show (default: 50)"
)
@click.option("--offset", default=0, help="Number of samples to skip (default: 0)")
@click.option(
    "--show-metadata", is_flag=True, help="Show detailed metadata for each sample"
)
def samples(
    batch_id: Optional[int] = None,
    mode: Optional[str] = None,
    labels: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    show_metadata: bool = False,
):
    """Query samples with filters"""
    db = SessionLocal()
    try:
        sample_service = SampleService(db)
        batch_service = BatchService(db)

        console.print("[blue]🔍 Querying samples...[/blue]")

        # 处理过滤参数
        filters_info = []

        if batch_id:
            batch = batch_service.get_batch_by_id(batch_id)
            if not batch:
                console.print(f"[red]❌ Batch with ID {batch_id} not found[/red]")
                raise click.Abort()
            filters_info.append(f"Batch: {batch.name} (ID: {batch_id})")

        sample_mode = None
        if mode:
            sample_mode = SampleMode(mode)
            filters_info.append(f"Mode: {mode}")

        label_list = None
        if labels:
            label_list = [label.strip() for label in labels.split(",")]
            filters_info.append(f"Labels: {', '.join(label_list)}")

        if offset > 0:
            filters_info.append(f"Offset: {offset}")

        # 显示过滤条件
        if filters_info:
            console.print(f"[blue]📋 Filters: {' | '.join(filters_info)}[/blue]")

        # 查询样本
        found_samples = sample_service.get_samples_with_filters(
            batch_id=batch_id,
            mode=sample_mode,
            labels=label_list,
            limit=limit,
            offset=offset,
        )

        if not found_samples:
            console.print("[yellow]No samples found matching the criteria.[/yellow]")
            return

        # 获取总数
        total_count = sample_service.count_samples_with_filters(
            batch_id=batch_id, mode=sample_mode, labels=label_list
        )

        # 创建结果表格
        if show_metadata:
            # 详细视图
            for i, sample in enumerate(found_samples, offset + 1):
                console.print(f"\n[bold cyan]Sample {i} (ID: {sample.id})[/bold cyan]")

                info_table = Table(show_header=False, box=None)
                info_table.add_column("Field", style="bold", width=15)
                info_table.add_column("Value", width=50)

                info_table.add_row(
                    "Batch", f"{sample.batch.name} (ID: {sample.batch_id})"
                )
                info_table.add_row("Mode", sample.mode.value.replace("_", " ").title())
                info_table.add_row("Image ID", str(sample.image_id))
                info_table.add_row(
                    "Labels", ", ".join(sample.labels) if sample.labels else "None"
                )
                info_table.add_row(
                    "Created", sample.created_at.strftime("%Y-%m-%d %H:%M:%S")
                )

                console.print(info_table)

                # 显示元数据
                if sample.sample_metadata:
                    console.print("[bold]Metadata:[/bold]")
                    for key, value in sample.sample_metadata.items():
                        if isinstance(value, list) and len(value) > 3:
                            # 对于长列表，只显示前几个元素
                            display_value = f"{value[:3]}... ({len(value)} items)"
                        else:
                            display_value = str(value)
                        console.print(f"  [cyan]{key}:[/cyan] {display_value}")

                if i < len(found_samples) + offset:
                    console.print("─" * 60)
        else:
            # 表格视图
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("ID", justify="right", width=6)
            table.add_column("Batch", width=20)
            table.add_column("Mode", width=15)
            table.add_column("Image ID", justify="right", width=8)
            table.add_column("Labels", width=25)
            table.add_column("Created", width=16)

            for sample in found_samples:
                table.add_row(
                    str(sample.id),
                    sample.batch.name[:18] + "..."
                    if len(sample.batch.name) > 18
                    else sample.batch.name,
                    sample.mode.value.replace("_", " ").title(),
                    str(sample.image_id),
                    ", ".join(sample.labels[:2])
                    + ("..." if len(sample.labels) > 2 else "")
                    if sample.labels
                    else "None",
                    sample.created_at.strftime("%m-%d %H:%M"),
                )

            console.print(Panel(table, title="🔍 Query Results", border_style="blue"))

        # 显示分页信息
        start_num = offset + 1
        end_num = min(offset + len(found_samples), total_count)
        console.print(
            f"\n[dim]Showing {start_num}-{end_num} of {total_count} total samples[/dim]"
        )

        if end_num < total_count:
            next_offset = offset + limit
            console.print(f"[dim]Use --offset={next_offset} to see more results[/dim]")

    except Exception as e:
        console.print(f"[red]❌ Query failed: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@query.command()
@click.option(
    "--limit", default=50, help="Maximum number of images to show (default: 50)"
)
@click.option(
    "--duplicates-only", is_flag=True, help="Show only images with multiple samples"
)
@click.option("--show-samples", is_flag=True, help="Show sample count for each image")
def images(limit: int = 50, duplicates_only: bool = False, show_samples: bool = False):
    """Query images and their usage"""
    db = SessionLocal()
    try:
        sample_service = SampleService(db)

        console.print("[blue]🖼️ Querying images...[/blue]")

        # 查询图片
        from sqlalchemy import func

        from ruyidv.models import Image, Sample

        query = db.query(Image)

        if duplicates_only:
            # 只显示有多个样本的图片
            query = (
                query.join(Sample).group_by(Image.id).having(func.count(Sample.id) > 1)
            )

        query = query.order_by(Image.created_at.desc()).limit(limit)
        images_list = query.all()

        if not images_list:
            if duplicates_only:
                console.print("[yellow]No images with multiple samples found.[/yellow]")
            else:
                console.print("[yellow]No images found.[/yellow]")
            return

        # 创建表格
        table = Table(show_header=True, header_style="bold cyan")
        table.add_column("ID", justify="right", width=6)
        table.add_column("Hash (Short)", width=12)
        table.add_column("File Path", width=35)
        table.add_column("Size", justify="right", width=10)
        table.add_column("Dimensions", width=12)
        if show_samples:
            table.add_column("Samples", justify="right", width=8)
        table.add_column("Created", width=16)

        for image in images_list:
            # 获取样本数量
            sample_count = (
                len(sample_service.get_samples_by_image(image.id))
                if show_samples
                else 0
            )

            # 格式化文件大小
            size_str = f"{image.file_size // 1024}KB" if image.file_size else "Unknown"

            # 格式化尺寸
            dims_str = (
                f"{image.width}×{image.height}"
                if image.width and image.height
                else "Unknown"
            )

            row_data = [
                str(image.id),
                image.hash_value[:10] + "...",
                image.file_path[-32:] + "..."
                if len(image.file_path) > 32
                else image.file_path,
                size_str,
                dims_str,
            ]

            if show_samples:
                row_data.append(str(sample_count))

            row_data.append(image.created_at.strftime("%m-%d %H:%M"))

            table.add_row(*row_data)

        title = "🖼️ Images with Multiple Samples" if duplicates_only else "🖼️ Images"
        console.print(Panel(table, title=title, border_style="blue"))

        console.print(f"\n[dim]Showing {len(images_list)} image(s)[/dim]")

    except Exception as e:
        console.print(f"[red]❌ Query failed: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@query.command()
@click.argument("keyword")
@click.option(
    "--limit", default=20, help="Maximum number of results to show (default: 20)"
)
def search(keyword: str, limit: int = 20):
    """Global search across batches, samples, and metadata"""
    db = SessionLocal()
    try:
        batch_service = BatchService(db)

        console.print(f"[blue]🔍 Searching for '{keyword}'...[/blue]")

        results_found = False

        # 搜索批次
        batches = batch_service.search_batches(keyword, limit=10)
        if batches:
            results_found = True
            console.print("\n[bold]📦 Matching Batches:[/bold]")

            batch_table = Table(show_header=True, header_style="bold green")
            batch_table.add_column("ID", justify="right", width=6)
            batch_table.add_column("Name", width=25)
            batch_table.add_column("Description", width=40)
            batch_table.add_column("Created", width=16)

            for batch in batches:
                batch_table.add_row(
                    str(batch.id),
                    batch.name,
                    (batch.description[:37] + "...")
                    if batch.description and len(batch.description) > 40
                    else (batch.description or ""),
                    batch.created_at.strftime("%m-%d %H:%M"),
                )

            console.print(batch_table)

        # 搜索样本标签
        from ruyidv.models import Sample

        # 在标签中搜索
        label_samples = (
            db.query(Sample)
            .filter(Sample.labels.contains([keyword]))
            .limit(limit)
            .all()
        )

        if label_samples:
            results_found = True
            console.print(f"\n[bold]🏷️ Samples with label '{keyword}':[/bold]")

            label_table = Table(show_header=True, header_style="bold yellow")
            label_table.add_column("ID", justify="right", width=6)
            label_table.add_column("Batch", width=20)
            label_table.add_column("Mode", width=15)
            label_table.add_column("All Labels", width=30)
            label_table.add_column("Created", width=16)

            for sample in label_samples:
                label_table.add_row(
                    str(sample.id),
                    sample.batch.name[:18] + "..."
                    if len(sample.batch.name) > 18
                    else sample.batch.name,
                    sample.mode.value.replace("_", " ").title(),
                    ", ".join(sample.labels[:3])
                    + ("..." if len(sample.labels) > 3 else "")
                    if sample.labels
                    else "",
                    sample.created_at.strftime("%m-%d %H:%M"),
                )

            console.print(label_table)

        # 在元数据中搜索（使用优化的数据库特定查询）
        try:
            from ruyidv.services.sample_service import SampleService

            sample_service = SampleService(db)
            metadata_samples = sample_service.search_metadata(keyword, limit)

            if metadata_samples:
                results_found = True
                console.print(
                    f"\n[bold]📄 Samples with '{keyword}' in metadata:[/bold]"
                )

                meta_table = Table(show_header=True, header_style="bold magenta")
                meta_table.add_column("ID", justify="right", width=6)
                meta_table.add_column("Batch", width=18)
                meta_table.add_column("Mode", width=15)
                meta_table.add_column("Metadata Preview", width=35)

                for sample in metadata_samples:
                    # 尝试在元数据中找到关键词
                    metadata_preview = ""
                    if sample.sample_metadata:
                        for key, value in sample.sample_metadata.items():
                            value_str = str(value).lower()
                            if keyword.lower() in value_str:
                                metadata_preview = f"{key}: {str(value)[:25]}..."
                                break

                    meta_table.add_row(
                        str(sample.id),
                        sample.batch.name[:15] + "..."
                        if len(sample.batch.name) > 15
                        else sample.batch.name,
                        sample.mode.value.replace("_", " ").title(),
                        metadata_preview,
                    )

                console.print(meta_table)
        except Exception:
            # 如果JSON搜索失败，跳过元数据搜索
            pass

        if not results_found:
            console.print(
                Panel.fit(
                    f"[yellow]No results found for '[bold]{keyword}[/bold]'[/yellow]\n\n"
                    "Try searching for:\n"
                    "• Batch names or descriptions\n"
                    "• Sample labels\n"
                    "• Keywords in metadata",
                    title="🔍 Search Results",
                    border_style="yellow",
                )
            )
        else:
            console.print(f"\n[dim]Search completed for '{keyword}'[/dim]")

    except Exception as e:
        console.print(f"[red]❌ Search failed: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@query.command()
@click.argument("sample_id", type=int)
def sample_detail(sample_id: int):
    """Show detailed information about a specific sample"""
    db = SessionLocal()
    try:
        sample_service = SampleService(db)
        image_service = ImageService(db)

        # 获取样本
        sample = sample_service.get_sample_by_id(sample_id)
        if not sample:
            console.print(f"[red]❌ Sample with ID {sample_id} not found[/red]")
            raise click.Abort()

        console.print(f"[bold cyan]Sample {sample_id} Details[/bold cyan]\n")

        # 基本信息
        basic_info = Table(show_header=False, box=None, padding=(0, 2))
        basic_info.add_column("Field", style="bold cyan", width=20)
        basic_info.add_column("Value", width=50)

        basic_info.add_row("Sample ID", str(sample.id))
        basic_info.add_row("Batch", f"{sample.batch.name} (ID: {sample.batch_id})")
        basic_info.add_row("Mode", sample.mode.value.replace("_", " ").title())
        basic_info.add_row("Image ID", str(sample.image_id))
        basic_info.add_row(
            "Labels", ", ".join(sample.labels) if sample.labels else "None"
        )
        basic_info.add_row("Created", sample.created_at.strftime("%Y-%m-%d %H:%M:%S"))

        console.print(
            Panel(basic_info, title="📋 Basic Information", border_style="blue")
        )

        # 图片信息
        image = sample.image
        if image:
            image_info = Table(show_header=False, box=None, padding=(0, 2))
            image_info.add_column("Field", style="bold green", width=20)
            image_info.add_column("Value", width=50)

            image_info.add_row("File Path", image.file_path)
            image_info.add_row("Hash", image.hash_value)
            image_info.add_row(
                "Dimensions",
                f"{image.width} × {image.height}"
                if image.width and image.height
                else "Unknown",
            )
            image_info.add_row(
                "File Size",
                f"{image.file_size:,} bytes" if image.file_size else "Unknown",
            )

            # 获取绝对路径
            abs_path = image_service.get_absolute_path(image)
            image_info.add_row("Absolute Path", str(abs_path))
            image_info.add_row(
                "File Exists", "✅ Yes" if abs_path.exists() else "❌ No"
            )

            console.print(
                Panel(image_info, title="🖼️ Image Information", border_style="green")
            )

        # 元数据
        if sample.sample_metadata:
            console.print("\n[bold yellow]📄 Metadata:[/bold yellow]")

            for key, value in sample.sample_metadata.items():
                if isinstance(value, (list, dict)):
                    console.print(f"  [cyan]{key}:[/cyan]")
                    if isinstance(value, list):
                        for i, item in enumerate(value):
                            console.print(f"    [{i}] {item}")
                    else:
                        for sub_key, sub_value in value.items():
                            console.print(f"    {sub_key}: {sub_value}")
                else:
                    console.print(f"  [cyan]{key}:[/cyan] {value}")
        else:
            console.print("\n[dim]No metadata available[/dim]")

        # 同一图片的其他样本
        other_samples = sample_service.get_samples_by_image(sample.image_id)
        other_samples = [s for s in other_samples if s.id != sample.id]

        if other_samples:
            console.print(
                "\n[bold magenta]🔗 Other samples using the same image:[/bold magenta]"
            )

            other_table = Table(show_header=True, header_style="bold magenta")
            other_table.add_column("ID", justify="right", width=6)
            other_table.add_column("Batch", width=20)
            other_table.add_column("Mode", width=15)
            other_table.add_column("Labels", width=25)

            for other in other_samples:
                other_table.add_row(
                    str(other.id),
                    other.batch.name[:18] + "..."
                    if len(other.batch.name) > 18
                    else other.batch.name,
                    other.mode.value.replace("_", " ").title(),
                    ", ".join(other.labels[:2])
                    + ("..." if len(other.labels) > 2 else "")
                    if other.labels
                    else "None",
                )

            console.print(other_table)

    except Exception as e:
        console.print(f"[red]❌ Failed to get sample details: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()
