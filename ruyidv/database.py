from sqlalchemy import create_engine
from sqlalchemy.orm import DeclarativeBase, sessionmaker

from .config import settings


# 创建数据库引擎
def _get_engine_config():
    """Get database engine configuration based on database type"""
    if "sqlite" in settings.DATABASE_URL:
        # SQLite configuration (for backward compatibility)
        return {
            "connect_args": {"check_same_thread": False},
        }
    else:
        # PostgreSQL configuration with connection pooling
        return {
            "pool_pre_ping": True,  # Verify connections before use
            "pool_recycle": 3600,  # Recycle connections every hour
            "pool_size": 20,  # Connection pool size
            "max_overflow": 30,  # Additional connections beyond pool_size
        }


engine = create_engine(settings.DATABASE_URL, **_get_engine_config())

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


# 创建Base类
class Base(DeclarativeBase):
    pass


# 数据库会话依赖
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
