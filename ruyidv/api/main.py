"""
FastAPI main application
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from ..config import settings
from .middleware.security import (
    InputValidationMiddleware,
    RequestLoggingMiddleware,
    SecurityHeadersMiddleware,
)
from .routers import admin, annotation, auth, batches, samples, stats

# Calculate frontend path prefix for consistent use
frontend_prefix = settings.FRONTEND_PATH_PREFIX.rstrip('/') if settings.FRONTEND_PATH_PREFIX != '/' else ''

# 创建FastAPI应用实例
# Configure docs URLs with frontend path prefix
docs_url = f"{frontend_prefix}/docs" if frontend_prefix else "/docs"
redoc_url = f"{frontend_prefix}/redoc" if frontend_prefix else "/redoc"

app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    docs_url=docs_url,
    redoc_url=redoc_url,
)

# 添加安全中间件 (按执行顺序添加)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(InputValidationMiddleware)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由 - 包含前端路径前缀
# Combine frontend path prefix with API prefix for proper routing
api_prefix = f"{frontend_prefix}{settings.API_V1_PREFIX}"

app.include_router(auth.router, prefix=api_prefix)
app.include_router(stats.router, prefix=api_prefix)
app.include_router(annotation.router, prefix=api_prefix)
app.include_router(samples.router, prefix=api_prefix)
app.include_router(batches.router, prefix=api_prefix)
app.include_router(admin.router, prefix=api_prefix)


@app.get("/")
async def root():
    """API根端点"""
    return {
        "message": "Ruyi Dataverse API",
        "version": settings.VERSION,
        "docs": docs_url,
    }

# Add root endpoint for path prefix if configured
if frontend_prefix:
    @app.get(f"{frontend_prefix}/")
    async def root_with_prefix():
        """API根端点 (with path prefix)"""
        return {
            "message": "Ruyi Dataverse API",
            "version": settings.VERSION,
            "docs": docs_url,
        }


# Static file serving for frontend
if settings.SERVE_FRONTEND and settings.FRONTEND_STATIC_DIR.exists():
    from fastapi.staticfiles import StaticFiles
    from fastapi.responses import FileResponse
    from fastapi.exceptions import HTTPException

    # Mount static files for assets (use already calculated frontend_prefix)
    app.mount(
        f"{frontend_prefix}/assets",
        StaticFiles(directory=settings.FRONTEND_STATIC_DIR / "assets"),
        name="assets"
    )

    # Serve other static files (like vite.svg)
    @app.get(f"{frontend_prefix}/vite.svg")
    async def serve_vite_svg():
        file_path = settings.FRONTEND_STATIC_DIR / "vite.svg"
        if file_path.exists():
            return FileResponse(file_path)
        raise HTTPException(status_code=404, detail="File not found")

    # SPA fallback route - serve index.html for all non-API routes
    @app.get("/{full_path:path}")
    async def serve_spa(full_path: str):
        # Don't serve SPA for API routes, docs, or assets
        if (full_path.startswith("api/") or
            full_path.startswith("docs") or
            full_path.startswith("redoc") or
            full_path.startswith("assets/")):
            raise HTTPException(status_code=404, detail="Not found")

        # For root path prefix, serve all remaining routes
        if frontend_prefix == '':
            index_path = settings.FRONTEND_STATIC_DIR / "index.html"
            if index_path.exists():
                return FileResponse(index_path)
            raise HTTPException(status_code=404, detail="Frontend not found")

        # For non-root path prefix, check if path starts with prefix
        prefix_without_slash = frontend_prefix.lstrip('/')
        if not full_path.startswith(prefix_without_slash):
            raise HTTPException(status_code=404, detail="Not found")

        # Serve index.html for SPA routing
        index_path = settings.FRONTEND_STATIC_DIR / "index.html"
        if index_path.exists():
            return FileResponse(index_path)
        raise HTTPException(status_code=404, detail="Frontend not found")
