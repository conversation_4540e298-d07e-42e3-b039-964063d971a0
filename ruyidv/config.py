import os
from pathlib import Path
from typing import Any, Optional

import yaml


class Settings:
    """Ruyi Dataverse Configuration Settings"""

    # Type annotations for dynamic attributes
    DATABASE_URL: str
    STORAGE_ROOT: Path
    IMAGES_DIR: Path
    ALLOWED_IMAGE_EXTENSIONS: list[str]
    MAX_FILE_SIZE: int
    USE_SYMLINKS: bool
    DEFAULT_IMAGE_SUBDIR: str
    EXPORT_MAX_WORKERS: int
    VERIFY_EXPORTED_IMAGES: bool
    EXPORT_MAPREDUCE_THRESHOLD: int
    EXPORT_MAPREDUCE_CHUNK_SIZE: int
    IMPORT_MAX_WORKERS: int
    IMPORT_MAPREDUCE_THRESHOLD: int
    IMPORT_MAPREDUCE_CHUNK_SIZE: int
    USER_HOME: Path
    USER_CONFIG_DIR: Path
    USER_FORMATTERS_DIR: Path
    API_V1_PREFIX: str
    PROJECT_NAME: str
    FRONTEND_PATH_PREFIX: str
    FRONTEND_STATIC_DIR: Path
    SERVE_FRONTEND: bool
    VERSION: str
    DESCRIPTION: str
    HOST: str
    PORT: int
    DEBUG: bool
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int
    CORS_ORIGINS: list[str]
    SESSION_TIMEOUT_MINUTES: int
    SESSION_IDLE_TIMEOUT_MINUTES: int
    MAX_LOGIN_ATTEMPTS: int
    LOGIN_ATTEMPT_WINDOW_MINUTES: int
    LOG_LEVEL: str

    def __init__(self):
        # Default configuration values
        self._defaults = {
            # Database Configuration
            "DATABASE_URL": "postgresql://ruyidv:ruyidv@localhost:5432/ruyidv",
            # Storage Configuration
            "STORAGE_ROOT": Path("storage"),
            # Image Processing Configuration
            "ALLOWED_IMAGE_EXTENSIONS": [".jpg", ".jpeg", ".png", ".webp", ".bmp"],
            "MAX_FILE_SIZE": 50 * 1024 * 1024,  # 50MB
            "USE_SYMLINKS": True,  # 优先使用软链接而非复制文件
            # Export Configuration
            "DEFAULT_IMAGE_SUBDIR": "images",
            "EXPORT_MAX_WORKERS": 128,  # 并行复制线程数（预留）
            "VERIFY_EXPORTED_IMAGES": True,  # 验证导出图片完整性
            "EXPORT_MAPREDUCE_THRESHOLD": 1000,  # 使用Map-Reduce模式的样本数阈值
            "EXPORT_MAPREDUCE_CHUNK_SIZE": 1000,  # Map-Reduce模式的块大小
            # Import Configuration
            "IMPORT_MAX_WORKERS": 128,  # 并行导入图片处理线程数
            "IMPORT_MAPREDUCE_THRESHOLD": 1000,  # 使用Map-Reduce模式的样本数阈值
            "IMPORT_MAPREDUCE_CHUNK_SIZE": 1000,  # Map-Reduce模式的块大小
            # User Configuration
            "USER_HOME": Path.home(),
            "USER_CONFIG_DIR": Path.home() / ".ruyidv",
            "USER_FORMATTERS_DIR": Path.home() / ".ruyidv" / "formatters",
            # API Configuration
            "API_V1_PREFIX": "/api/v1",
            "PROJECT_NAME": "Ruyi Dataverse",
            # Frontend Configuration
            "FRONTEND_PATH_PREFIX": "/",  # Path prefix for frontend deployment
            "FRONTEND_STATIC_DIR": Path("frontend/dist"),  # Frontend build directory
            "SERVE_FRONTEND": True,  # Whether to serve frontend static files
            # Admin Dashboard Configuration
            "ADMIN_DASHBOARD_SUBDOMAIN": None,  # Will be set via CLI
            "ADMIN_DASHBOARD_ENABLED": False,
            "VERSION": self._get_version(),
            "DESCRIPTION": "Training data management for Ruyi Grounding Model",
            # Server Configuration
            "HOST": "127.0.0.1",
            "PORT": 8000,
            "DEBUG": False,
            # Security Configuration
            "SECRET_KEY": "your-secret-key-here",
            "ACCESS_TOKEN_EXPIRE_MINUTES": 30,
            # CORS Configuration
            "CORS_ORIGINS": [
                "http://localhost:3000",
                "http://127.0.0.1:3000",
                "http://localhost:3001",
                "http://127.0.0.1:3001",
            ],
            "SESSION_TIMEOUT_MINUTES": 60,  # Session timeout for annotators
            "SESSION_IDLE_TIMEOUT_MINUTES": 30,  # Idle timeout (no activity)
            "MAX_LOGIN_ATTEMPTS": 5,  # Rate limiting for login attempts
            "LOGIN_ATTEMPT_WINDOW_MINUTES": 15,  # Time window for rate limiting
            # Logging Configuration
            "LOG_LEVEL": "INFO",
        }

        # Load configuration from YAML and environment variables
        self._load_configuration()
        self._ensure_directories()

    def _get_version(self) -> str:
        """Get the version from package metadata"""
        import importlib.metadata

        return importlib.metadata.version("ruyidv")

    def _load_configuration(self):
        """Load configuration from YAML file and environment variables"""
        # Start with defaults
        config = self._defaults.copy()

        # Load from YAML file
        yaml_config = self._load_yaml_config()
        if yaml_config:
            self._apply_yaml_config(config, yaml_config)

        # Override with environment variables
        self._apply_env_config(config)

        # Set attributes
        for key, value in config.items():
            setattr(self, key, value)

        # Set derived paths
        self.IMAGES_DIR = self.STORAGE_ROOT / "images"

    def _load_yaml_config(self) -> Optional[dict[str, Any]]:
        """Load configuration from YAML file"""
        # Use the default user config directory path
        config_file = Path.home() / ".ruyidv" / "config.yaml"
        if not config_file.exists():
            return None

        try:
            with open(config_file, "r", encoding="utf-8") as f:
                return yaml.safe_load(f) or {}
        except Exception:
            # Silently ignore YAML loading errors to avoid breaking the application
            return None

    def _apply_yaml_config(self, config: dict[str, Any], yaml_config: dict[str, Any]):
        """Apply YAML configuration to config dict"""
        # Map YAML structure to Settings attributes
        yaml_mappings = {
            ("database", "url"): "DATABASE_URL",
            ("storage", "root"): "STORAGE_ROOT",
            ("api", "host"): "HOST",
            ("api", "port"): "PORT",
            ("api", "debug"): "DEBUG",
            ("import", "max_batch_size"): "MAX_BATCH_SIZE",
            ("export", "default_limit"): "EXPORT_DEFAULT_LIMIT",
            ("export", "default_format"): "EXPORT_DEFAULT_FORMAT",
            # Frontend configuration
            ("frontend", "path_prefix"): "FRONTEND_PATH_PREFIX",
            ("frontend", "serve_frontend"): "SERVE_FRONTEND",
            ("frontend", "cors_origins"): "CORS_ORIGINS",
        }

        for yaml_path, setting_key in yaml_mappings.items():
            value = yaml_config
            try:
                for key in yaml_path:
                    value = value[key]

                # Convert paths to Path objects
                if setting_key in ["STORAGE_ROOT"] and isinstance(value, str):
                    value = Path(value)

                config[setting_key] = value
            except (KeyError, TypeError):
                continue

    def _apply_env_config(self, config: dict[str, Any]):
        """Apply environment variable overrides"""
        env_mappings = {
            "RUYIDV_DATABASE_URL": "DATABASE_URL",
            "RUYIDV_STORAGE_ROOT": "STORAGE_ROOT",
            "RUYIDV_HOST": "HOST",
            "RUYIDV_PORT": "PORT",
            "RUYIDV_DEBUG": "DEBUG",
            "DATABASE_URL": "DATABASE_URL",  # Common env var
            "HOST": "HOST",
            "PORT": "PORT",
            "DEBUG": "DEBUG",
        }

        for env_var, setting_key in env_mappings.items():
            value = os.environ.get(env_var)
            if value is not None:
                # Type conversion
                if setting_key == "PORT":
                    try:
                        value = int(value)
                    except ValueError:
                        continue
                elif setting_key == "DEBUG":
                    value = value.lower() in ("true", "1", "yes", "on")
                elif setting_key == "STORAGE_ROOT":
                    value = Path(value)

                config[setting_key] = value

    def _ensure_directories(self):
        """Ensure required directories exist"""
        self.STORAGE_ROOT.mkdir(exist_ok=True)
        self.IMAGES_DIR.mkdir(parents=True, exist_ok=True)
        self.USER_CONFIG_DIR.mkdir(exist_ok=True)
        self.USER_FORMATTERS_DIR.mkdir(exist_ok=True)

    @property
    def database_url_sync(self) -> str:
        """同步数据库URL"""
        return self.DATABASE_URL

    @property
    def database_url_async(self) -> str:
        """异步数据库URL（如需要）"""
        if "sqlite" in self.DATABASE_URL:
            return self.DATABASE_URL.replace("sqlite://", "sqlite+aiosqlite://")
        return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")

    def reload_config(self):
        """Reload configuration from files and environment variables"""
        self._load_configuration()
        self._ensure_directories()


# Global settings instance
settings = Settings()
