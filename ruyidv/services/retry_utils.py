"""
Retry utilities for Ruyi Dataverse

This module provides retry logic with exponential backoff for handling
transient failures in database and file operations.
"""

import random
import time
from functools import wraps
from typing import Any, Callable, Optional, Type, Union


def retry_with_backoff(
    max_retries: int = 3,
    base_delay: float = 0.1,
    max_delay: float = 2.0,
    backoff_factor: float = 2.0,
    jitter: bool = True,
    exceptions: Union[Type[Exception], tuple[Type[Exception], ...]] = Exception,
):
    """
    Decorator for retrying functions with exponential backoff

    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        backoff_factor: Multiplier for delay between retries
        jitter: Whether to add random jitter to delay
        exceptions: Exception types to retry on

    Returns:
        Decorated function with retry logic
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None

            for attempt in range(max_retries + 1):  # +1 for initial attempt
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e

                    if attempt == max_retries:
                        # Last attempt failed, re-raise the exception
                        raise e

                    # Calculate delay with exponential backoff
                    delay = min(base_delay * (backoff_factor**attempt), max_delay)

                    # Add jitter to prevent thundering herd
                    if jitter:
                        delay += random.uniform(0, delay * 0.1)

                    time.sleep(delay)

            # This should never be reached, but just in case
            if last_exception:
                raise last_exception

        return wrapper

    return decorator


def is_retryable_db_error(error: Exception) -> bool:
    """
    Check if a database error is retryable

    Args:
        error: Exception to check

    Returns:
        True if the error is retryable, False otherwise
    """
    error_msg = str(error).lower()

    # PostgreSQL specific retryable errors
    retryable_patterns = [
        "unique constraint",  # Constraint violations (race conditions)
        "deadlock detected",  # Deadlocks
        "connection",  # Connection issues
        "timeout",  # Timeout errors
        "lock",  # Lock-related errors
        "serialization failure",  # Serialization conflicts
    ]

    return any(pattern in error_msg for pattern in retryable_patterns)


def is_retryable_file_error(error: Exception) -> bool:
    """
    Check if a file operation error is retryable

    Args:
        error: Exception to check

    Returns:
        True if the error is retryable, False otherwise
    """
    error_msg = str(error).lower()

    # File operation retryable errors
    retryable_patterns = [
        "resource temporarily unavailable",
        "device or resource busy",
        "no space left on device",  # Might be temporary
        "permission denied",  # Might be temporary
        "file exists",  # Race condition
    ]

    # Non-retryable patterns (permanent errors)
    non_retryable_patterns = [
        "no such file or directory",
        "not a directory",
        "same file",  # This should be handled differently
    ]

    # Check for non-retryable patterns first
    if any(pattern in error_msg for pattern in non_retryable_patterns):
        return False

    return any(pattern in error_msg for pattern in retryable_patterns)


class RetryableOperation:
    """Context manager for retryable operations with custom logic"""

    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 0.1,
        max_delay: float = 2.0,
        backoff_factor: float = 2.0,
        jitter: bool = True,
        retry_condition: Optional[Callable[[Exception], bool]] = None,
    ):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        self.jitter = jitter
        self.retry_condition = retry_condition or (lambda _e: True)

        self.attempt = 0
        self.last_exception = None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, _exc_tb):
        if exc_type is None:
            return False  # No exception, continue normally

        self.last_exception = exc_val

        # Check if we should retry
        if self.attempt < self.max_retries and self.retry_condition(exc_val):
            # Calculate delay
            delay = min(
                self.base_delay * (self.backoff_factor**self.attempt), self.max_delay
            )

            if self.jitter:
                delay += random.uniform(0, delay * 0.1)

            time.sleep(delay)
            self.attempt += 1
            return True  # Suppress the exception and retry

        return False  # Don't suppress the exception

    def should_retry(self) -> bool:
        """Check if we should continue retrying"""
        return self.attempt < self.max_retries
