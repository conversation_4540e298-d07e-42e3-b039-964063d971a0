import json
import random
import shutil
from collections import defaultdict
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple

from PIL import Image as PILImage
from rich.console import Console
from rich.progress import (
    BarColumn,
    MofNCompleteColumn,
    Progress,
    SpinnerColumn,
    TextColumn,
    TimeRemainingColumn,
)
from sqlalchemy.orm import Session

from ..config import settings
from ..models import Sample, SampleMode
from .image_service import ImageService
from .sample_service import SampleService


class ExportService:
    """数据导出服务"""

    def __init__(self, db: Session):
        self.db = db
        self.sample_service = SampleService(db)
        self.image_service = ImageService(db)
        # 线程锁用于确保目录创建的线程安全
        import threading

        self._dir_creation_lock = threading.Lock()

    def get_default_formatter(self, mode: SampleMode):
        """
        获取指定模式的默认格式化器函数

        Args:
            mode: 样本模式

        Returns:
            Callable: 默认格式化器函数
        """

        def grounding_default_formatter(metadata, **kwargs):
            """默认grounding格式化器"""
            description = metadata.get("description", "")
            coordinate = metadata.get("coordinate", [None, None, None, None])

            instruction = (
                f"Find the location of: {description}"
                if description
                else "Find the specified object in the image"
            )

            if any(c is not None for c in coordinate):
                output = f"[{coordinate[0]}, {coordinate[1]}, {coordinate[2]}, {coordinate[3]}]"
            else:
                output = "Object not found"

            return {"instruction": instruction, "output": output}

        def describe_default_formatter(metadata, **kwargs):
            """默认describe格式化器"""
            coordinate = metadata.get("coordinate", [None, None, None, None])
            description = metadata.get("description", "")

            if any(c is not None for c in coordinate):
                instruction = f"Describe what you see at coordinates [{coordinate[0]}, {coordinate[1]}, {coordinate[2]}, {coordinate[3]}]"
            else:
                instruction = "Describe what you see in the image"

            output = description if description else "No description available"

            return {"instruction": instruction, "output": output}

        def enumerate_text_default_formatter(metadata, **kwargs):
            """默认enumerate_text格式化器"""
            general_desc = metadata.get("general_description", "")
            elements = metadata.get("elements", [])

            instruction = (
                f"List all text elements in this image. {general_desc}"
                if general_desc
                else "List all text elements in this image"
            )

            if elements:
                text_elements = [
                    elem.get("text", "") for elem in elements if elem.get("text")
                ]
                output = (
                    ", ".join(text_elements)
                    if text_elements
                    else "No text elements found"
                )
            else:
                output = "No text elements found"

            return {"instruction": instruction, "output": output}

        def enumerate_coord_default_formatter(metadata, **kwargs):
            """默认enumerate_coord格式化器"""
            general_desc = metadata.get("general_description", "")
            elements = metadata.get("elements", [])

            instruction = (
                f"List all element coordinates in this image. {general_desc}"
                if general_desc
                else "List all element coordinates in this image"
            )

            if elements:
                coords = []
                for elem in elements:
                    coord = elem.get("coordinate", [])
                    if coord and len(coord) >= 4:
                        coords.append(
                            f"[{coord[0]}, {coord[1]}, {coord[2]}, {coord[3]}]"
                        )
                output = ", ".join(coords) if coords else "No coordinates found"
            else:
                output = "No coordinates found"

            return {"instruction": instruction, "output": output}

        def checklist_default_formatter(metadata, **kwargs):
            """默认checklist格式化器"""
            descriptions = metadata.get("descriptions", [])
            results = metadata.get("results", [])

            instruction = (
                "Check the following items in the image: " + ", ".join(descriptions)
                if descriptions
                else "Check items in the image"
            )

            if results:
                output = ", ".join([
                    f"{desc}: {'Yes' if res else 'No'}"
                    for desc, res in zip(descriptions, results)
                ])
            else:
                output = "No checklist results available"

            return {"instruction": instruction, "output": output}

        def ensure_default_formatter(metadata, **kwargs):
            """默认ensure格式化器"""
            expected_state = metadata.get("expected_state", "")
            coordinate = metadata.get("coordinate", [None, None, None, None])

            if any(c is not None for c in coordinate):
                instruction = f"Ensure the area at [{coordinate[0]}, {coordinate[1]}, {coordinate[2]}, {coordinate[3]}] has the expected state: {expected_state}"
            else:
                instruction = f"Ensure the expected state: {expected_state}"

            output = (
                f"Expected state: {expected_state}"
                if expected_state
                else "No expected state specified"
            )

            return {"instruction": instruction, "output": output}

        # 根据模式返回相应的默认格式化器
        default_formatters = {
            SampleMode.GROUNDING: grounding_default_formatter,
            SampleMode.DESCRIBE: describe_default_formatter,
            SampleMode.ENUMERATE_TEXT: enumerate_text_default_formatter,
            SampleMode.ENUMERATE_COORD: enumerate_coord_default_formatter,
            SampleMode.CHECKLIST: checklist_default_formatter,
            SampleMode.ENSURE: ensure_default_formatter,
        }

        return default_formatters.get(mode, grounding_default_formatter)

    def apply_custom_formatter(self, sample: Sample, formatter_func) -> Dict[str, Any]:
        """
        应用自定义格式化器函数

        Args:
            sample: 样本数据
            formatter_func: 格式化器函数

        Returns:
            Dict: 包含格式化后的指令、输出和可选图像处理函数的字典
        """
        # 获取图像信息
        image_info = self._get_image_info_for_formatter(sample.image)

        kwargs = {
            "mode": sample.mode.value,
            "labels": sample.labels or [],
            "image_id": sample.image_id,
            "batch_id": sample.batch_id,
            "sample": sample,
            # 图像信息
            "image_width": image_info["width"],
            "image_height": image_info["height"],
            "image_size": image_info["file_size"],
            "image_hash": image_info["hash_value"],
            "image_path": image_info["file_path"],
            "image_info": image_info,  # 完整的图像信息字典
        }

        result = formatter_func(sample.sample_metadata, **kwargs)

        # 确保返回值包含必要的字段
        if (
            not isinstance(result, dict)
            or "instruction" not in result
            or "output" not in result
        ):
            raise ValueError(
                "Custom formatter must return dict with 'instruction' and 'output' keys"
            )

        formatted_result: Dict[str, Any] = {
            "instruction": str(result["instruction"]),
            "output": str(result["output"]),
        }

        # 检查是否包含图像处理函数
        if "image_processor" in result and result["image_processor"] is not None:
            if not callable(result["image_processor"]):
                raise ValueError("image_processor must be callable")
            formatted_result["image_processor"] = result["image_processor"]

        return formatted_result

    def _get_image_info_for_formatter(self, image) -> Dict[str, Any]:
        """
        获取图像信息用于formatter

        Args:
            image: Image模型实例

        Returns:
            Dict: 包含图像信息的字典
        """
        try:
            # 获取图像的绝对路径
            absolute_path = self.image_service.get_absolute_path(image)

            # 构建图像信息字典
            image_info = {
                "width": image.width or 0,
                "height": image.height or 0,
                "file_size": image.file_size,
                "hash_value": image.hash_value,
                "file_path": str(absolute_path),
                "relative_path": image.file_path,
                "created_at": image.created_at.isoformat()
                if image.created_at
                else None,
            }

            # 如果图像文件存在，尝试获取更详细的信息
            if absolute_path.exists():
                try:
                    with PILImage.open(absolute_path) as img:
                        image_info.update({
                            "mode": img.mode,  # 图像模式 (RGB, RGBA, L, etc.)
                            "format": img.format,  # 图像格式 (JPEG, PNG, etc.)
                            "has_transparency": img.mode in ("RGBA", "LA")
                            or "transparency" in img.info,
                        })

                        # 如果数据库中的尺寸信息缺失，使用实际图像尺寸
                        if not image.width or not image.height:
                            image_info["width"], image_info["height"] = img.size

                except Exception:
                    # 如果无法读取图像，使用数据库中的信息
                    pass

            return image_info

        except Exception as e:
            # 如果获取图像信息失败，返回基本信息
            return {
                "width": image.width or 0,
                "height": image.height or 0,
                "file_size": image.file_size,
                "hash_value": image.hash_value,
                "file_path": "",
                "relative_path": image.file_path,
                "created_at": image.created_at.isoformat()
                if image.created_at
                else None,
                "mode": "unknown",
                "format": "unknown",
                "has_transparency": False,
                "error": str(e),
            }

    def process_image_with_formatter(
        self,
        image_path: Path,
        image_processor: Callable[[PILImage.Image], PILImage.Image],
        console: Optional[Console] = None,
    ) -> Optional[PILImage.Image]:
        """
        使用formatter提供的图像处理函数处理图像

        Args:
            image_path: 图像文件路径
            image_processor: 图像处理函数
            console: Rich Console对象

        Returns:
            Optional[PIL.Image.Image]: 处理后的图像对象，失败时返回None
        """
        try:
            # 加载原始图像
            with PILImage.open(image_path) as original_image:
                # 转换为RGB模式以确保兼容性
                if original_image.mode != "RGB":
                    original_image = original_image.convert("RGB")

                # 应用图像处理函数
                processed_image = image_processor(original_image.copy())

                # 验证处理后的图像
                if not isinstance(processed_image, PILImage.Image):
                    raise ValueError(
                        f"Image processor returned {type(processed_image)}, expected PIL.Image.Image"
                    )

                return processed_image

        except Exception as e:
            if console:
                console.print(f"[red]Error processing image {image_path}: {e}[/red]")
            return None

    def generate_absolute_paths(self, samples: List[Sample]) -> Dict[int, str]:
        """生成图片绝对路径映射，使用sample.id作为键以保持一致性"""
        image_paths = {}

        for sample in samples:
            image = sample.image
            absolute_path = self.image_service.get_absolute_path(image)
            image_paths[sample.id] = str(absolute_path)

        return image_paths

    def copy_images_for_export(
        self,
        samples: List[Sample],
        export_dir: Path,
        image_subdir: str = "images",
        console: Optional[Console] = None,
    ) -> Tuple[Dict[int, str], List[str]]:
        """
        复制导出相关的图片到指定目录

        Args:
            samples: 样本列表
            export_dir: 导出根目录
            image_subdir: 图片子目录名
            console: Rich Console对象，用于显示进度

        Returns:
            Tuple[Dict[int, str], List[str]]: (图片ID到新路径的映射, 错误信息列表)
        """
        if console is None:
            console = Console()

        # 创建图片目录
        images_dir = export_dir / image_subdir
        images_dir.mkdir(parents=True, exist_ok=True)

        # 收集需要复制的唯一图片
        unique_images = {}  # image_id -> Image对象
        for sample in samples:
            if sample.image_id not in unique_images:
                unique_images[sample.image_id] = sample.image

        console.print(
            f"[blue]📷 Found {len(unique_images)} unique images to copy[/blue]"
        )

        # 复制图片并生成新路径映射
        image_paths = {}  # image_id -> 新的相对路径
        errors = []

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("Copying images...", total=len(unique_images))

            for i, (image_id, image) in enumerate(unique_images.items()):
                try:
                    # 获取源文件路径
                    source_path = self.image_service.get_absolute_path(image)

                    if not source_path.exists():
                        error_msg = f"Source image not found: {source_path}"
                        errors.append(error_msg)
                        console.print(f"[yellow]Warning: {error_msg}[/yellow]")
                        progress.update(task, advance=1)
                        continue

                    # 保持原有的哈希命名
                    # 从原文件路径获取扩展名
                    original_extension = source_path.suffix
                    target_filename = f"{image.hash_value}{original_extension}"
                    target_path = images_dir / target_filename

                    # 检查是否需要复制（增量复制）
                    if target_path.exists():
                        # 验证文件完整性
                        if self._verify_file_integrity(source_path, target_path):
                            # 文件已存在且完整，跳过复制
                            relative_path = Path(image_subdir) / target_filename
                            image_paths[image_id] = str(relative_path)
                            progress.update(
                                task,
                                advance=1,
                                description=f"Copying images... ({i + 1}/{len(unique_images)}) - skipped (exists)",
                            )
                            continue
                        else:
                            # 文件存在但不完整，需要重新复制
                            console.print(
                                f"[yellow]File integrity check failed, re-copying: {target_filename}[/yellow]"
                            )

                    # 复制文件
                    try:
                        # 解析软链接获取真实文件
                        if source_path.is_symlink():
                            source_path = source_path.resolve()

                        shutil.copy2(source_path, target_path)

                        # 验证复制后的文件完整性
                        if not self._verify_file_integrity(source_path, target_path):
                            error_msg = (
                                f"Copy verification failed for image {image.hash_value}"
                            )
                            errors.append(error_msg)
                            # 删除损坏的复制文件
                            if target_path.exists():
                                target_path.unlink()
                            progress.update(task, advance=1)
                            continue

                    except Exception as e:
                        error_msg = f"Failed to copy image {image.hash_value}: {str(e)}"
                        errors.append(error_msg)
                        console.print(f"[red]Error: {error_msg}[/red]")
                        progress.update(task, advance=1)
                        continue

                    # 记录新路径（相对于导出目录的路径）
                    relative_path = Path(image_subdir) / target_filename
                    image_paths[image_id] = str(relative_path)

                    progress.update(
                        task,
                        advance=1,
                        description=f"Copying images... ({i + 1}/{len(unique_images)})",
                    )

                except Exception as e:
                    error_msg = (
                        f"Unexpected error processing image {image_id}: {str(e)}"
                    )
                    errors.append(error_msg)
                    console.print(f"[red]Error: {error_msg}[/red]")
                    progress.update(task, advance=1)

        success_count = len(image_paths)
        if errors:
            console.print(
                f"[yellow]⚠️ Copied {success_count}/{len(unique_images)} images with {len(errors)} errors[/yellow]"
            )
        else:
            console.print(
                f"[green]✓[/green] Successfully copied all {success_count} images"
            )

        return image_paths, errors

    def _verify_file_integrity(self, source_path: Path, target_path: Path) -> bool:
        """
        验证文件完整性（通过文件大小和哈希值）

        Args:
            source_path: 源文件路径
            target_path: 目标文件路径

        Returns:
            bool: 文件是否完整
        """
        try:
            # 检查文件大小
            if source_path.stat().st_size != target_path.stat().st_size:
                return False

            # 计算并比较哈希值（使用与ImageService相同的方法）
            source_hash = self.image_service.calculate_image_hash(source_path)
            target_hash = self.image_service.calculate_image_hash(target_path)

            return source_hash == target_hash
        except Exception:
            return False

    def generate_image_paths(
        self,
        samples: List[Sample],
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
    ) -> Tuple[Dict[int, str], List[str]]:
        """
        生成图片路径映射，支持复制模式和链接模式

        Args:
            samples: 样本列表
            copy_images: 是否复制图片
            export_dir: 导出目录（copy_images为True时必需）
            image_subdir: 图片子目录名

        Returns:
            Tuple[Dict[int, str], List[str]]: (图片ID到路径的映射, 错误信息列表)
        """
        if copy_images:
            if export_dir is None:
                raise ValueError("export_dir is required when copy_images=True")
            return self.copy_images_for_export(samples, export_dir, image_subdir)
        else:
            # 原有的绝对路径模式
            image_paths = self.generate_absolute_paths(samples)
            return image_paths, []

    def save_export_file(
        self,
        data: List[Dict[str, Any]],
        filename: str,
        export_dir: Optional[Path] = None,
    ) -> Path:
        """
        保存导出文件

        Args:
            data: 要导出的数据
            filename: 文件名
            export_dir: 导出目录，如果为None则使用默认目录

        Returns:
            Path: 保存的文件路径
        """
        if export_dir is None:
            export_dir = settings.STORAGE_ROOT / "exports"
            export_dir.mkdir(exist_ok=True)

        file_path = export_dir / filename

        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        return file_path

    def get_export_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取导出数据统计信息"""
        total_items = len(data)

        # 统计图片数量
        unique_images = set()
        for item in data:
            for image_path in item.get("images", []):
                unique_images.add(image_path)

        return {
            "total_items": total_items,
            "unique_images": len(unique_images),
            "export_format": "sharegpt",
        }

    def create_export_summary(
        self, data: List[Dict[str, Any]], export_path: Path, filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建导出摘要"""
        stats = self.get_export_statistics(data)

        return {
            "export_path": str(export_path),
            "export_time": str(Path(export_path).stat().st_mtime),
            "filters_applied": filters,
            "statistics": stats,
            "success": True,
        }

    def export_with_filters_progress(
        self,
        batch_ids: Optional[List[int]] = None,
        modes: Optional[List[SampleMode]] = None,
        labels: Optional[List[str]] = None,
        formatter: Optional[str] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        console: Optional[Console] = None,
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        根据过滤条件导出数据，并显示进度

        Args:
            batch_ids: 批次ID列表过滤
            modes: 模式列表过滤
            labels: 标签过滤
            formatter: 自定义格式化器名称
            limit: 导出数量限制，None表示全部导出
            offset: 偏移量
            copy_images: 是否复制图片到导出目录
            export_dir: 导出目录（copy_images为True时必需）
            image_subdir: 图片子目录名
            console: Rich Console对象，用于显示进度

        Returns:
            Tuple[List[Dict[str, Any]], List[str]]: (ShareGPT格式的导出数据, 错误信息列表)
        """
        if console is None:
            console = Console()

        # 先获取总数量用于进度显示
        console.print("[blue]🔍 Counting samples...[/blue]")
        total_count = self.sample_service.count_samples_with_filters(
            batch_ids=batch_ids, modes=modes, labels=labels
        )

        if total_count == 0:
            return [], []

        # 确定实际导出数量
        actual_export_count = (
            min(total_count - offset, limit)
            if limit is not None
            else (total_count - offset)
        )

        if actual_export_count <= 0:
            return [], []

        console.print(
            f"[blue]📊 Found {total_count} total samples, will export {actual_export_count} samples[/blue]"
        )

        # 获取符合条件的样本
        samples = self.sample_service.get_samples_with_filters(
            batch_ids=batch_ids, modes=modes, labels=labels, limit=limit, offset=offset
        )

        if not samples:
            return [], []

        # 格式化为ShareGPT格式，带进度显示
        return self.format_sharegpt_output_with_progress(
            samples, formatter, copy_images, export_dir, image_subdir, console
        )

    def format_sharegpt_output_with_progress(
        self,
        samples: List[Sample],
        formatter: Optional[str] = None,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        console: Optional[Console] = None,
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        格式化为ShareGPT格式，带进度显示

        Args:
            samples: 样本列表
            formatter: 自定义格式化器名称
            copy_images: 是否复制图片到导出目录
            export_dir: 导出目录（copy_images为True时必需）
            image_subdir: 图片子目录名
            console: Rich Console对象

        Returns:
            Tuple[List[Dict[str, Any]], List[str]]: (ShareGPT格式的数据列表, 错误信息列表)
        """
        return self._format_sharegpt_output_internal(
            samples,
            formatter,
            copy_images,
            export_dir,
            image_subdir,
            console,
            show_progress=True,
        )

    def _format_sharegpt_output_internal(
        self,
        samples: List[Sample],
        formatter: Optional[str] = None,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        console: Optional[Console] = None,
        show_progress: bool = True,
        unified_image_paths: Optional[Dict[int, str]] = None,
        prescanned_formats: Optional[Dict[int, Dict[str, Any]]] = None,
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        格式化为ShareGPT格式的内部实现，可选择是否显示进度

        Args:
            samples: 样本列表
            formatter: 自定义格式化器名称
            copy_images: 是否复制图片到导出目录
            export_dir: 导出目录（copy_images为True时必需）
            image_subdir: 图片子目录名
            console: Rich Console对象
            show_progress: 是否显示进度条

        Returns:
            Tuple[List[Dict[str, Any]], List[str]]: (ShareGPT格式的数据列表, 错误信息列表)
        """
        if console is None:
            console = Console()

        sharegpt_data = []
        format_errors = []

        # 如果指定了自定义格式化器，导入并获取函数
        formatter_func = None
        if formatter:
            from . import get_formatter, list_formatters

            formatter_func = get_formatter(formatter)
            if not formatter_func:
                available = list_formatters()
                raise ValueError(
                    f"Custom formatter '{formatter}' not found. Available formatters: {available}"
                )

        # 处理样本格式化，使用预扫描结果或重新应用格式化器
        sample_formats = {}  # sample_id -> formatted_result
        image_processors = {}  # sample_id -> image_processor_func

        if prescanned_formats is not None:
            # 使用预扫描的格式化结果
            if show_progress:
                console.print("[blue]🔄 Using prescanned formatter results...[/blue]")

            for sample in samples:
                if sample.id in prescanned_formats:
                    sample_formats[sample.id] = prescanned_formats[sample.id]
                    # 检查是否有图像处理器
                    if "image_processor" in prescanned_formats[sample.id]:
                        image_processors[sample.id] = prescanned_formats[sample.id][
                            "image_processor"
                        ]
        else:
            # 重新应用格式化器
            if show_progress:
                console.print("[blue]🔄 Processing formatters...[/blue]")

            if show_progress:
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    MofNCompleteColumn(),
                    TimeRemainingColumn(),
                    console=console,
                ) as progress:
                    task = progress.add_task(
                        "Applying formatters...", total=len(samples)
                    )

                    for i, sample in enumerate(samples):
                        try:
                            # 应用格式化
                            if formatter_func:
                                formatted = self.apply_custom_formatter(
                                    sample, formatter_func
                                )
                            else:
                                # 使用默认格式化器
                                default_formatter = self.get_default_formatter(
                                    sample.mode
                                )
                                formatted = self.apply_custom_formatter(
                                    sample, default_formatter
                                )

                            sample_formats[sample.id] = formatted

                            # 检查是否有图像处理器
                            if "image_processor" in formatted:
                                image_processors[sample.id] = formatted[
                                    "image_processor"
                                ]

                            # 更新进度
                            progress.update(task, advance=1)

                        except Exception as e:
                            error_msg = f"Failed to format sample {sample.id}: {e}"
                            format_errors.append(error_msg)
                            if show_progress:
                                console.print(f"[yellow]Warning: {error_msg}[/yellow]")
                            progress.update(task, advance=1)
                            continue
            else:
                # 不显示进度条的版本
                for sample in samples:
                    try:
                        # 应用格式化
                        if formatter_func:
                            formatted = self.apply_custom_formatter(
                                sample, formatter_func
                            )
                        else:
                            # 使用默认格式化器
                            default_formatter = self.get_default_formatter(sample.mode)
                            formatted = self.apply_custom_formatter(
                                sample, default_formatter
                            )

                        sample_formats[sample.id] = formatted

                        # 检查是否有图像处理器
                        if "image_processor" in formatted:
                            image_processors[sample.id] = formatted["image_processor"]

                    except Exception as e:
                        error_msg = f"Failed to format sample {sample.id}: {e}"
                        format_errors.append(error_msg)
                        continue

        # 显示图像处理器统计
        if show_progress and image_processors:
            console.print(
                f"[blue]🎨 Found {len(image_processors)} samples with image processors[/blue]"
            )

        # 根据是否有图像处理器选择路径生成方式
        if unified_image_paths is not None:
            # 使用预先复制好的统一图片路径
            image_paths = unified_image_paths
            copy_errors = []
        else:
            if show_progress:
                console.print("[blue]📁 Generating image paths...[/blue]")
            if copy_images and image_processors:
                # 有图像处理器，使用带处理的复制方法
                if export_dir is None:
                    raise ValueError(
                        "export_dir is required when copy_images=True and image processors are present"
                    )
                image_paths, copy_errors = self.copy_images_with_processing(
                    samples,
                    export_dir,
                    image_processors,
                    image_subdir,
                    console if show_progress else None,
                    show_progress,
                )
            else:
                # 没有图像处理器，使用常规方法
                image_paths, copy_errors = self.generate_image_paths(
                    samples, copy_images, export_dir, image_subdir
                )

        # 构建最终的ShareGPT数据
        if show_progress:
            console.print("[blue]📝 Building ShareGPT output...[/blue]")
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                MofNCompleteColumn(),
                TimeRemainingColumn(),
                console=console,
            ) as progress:
                task = progress.add_task("Building output...", total=len(samples))

                for i, sample in enumerate(samples):
                    try:
                        # 检查样本格式化是否成功
                        if sample.id not in sample_formats:
                            progress.update(task, advance=1)
                            continue  # 格式化失败，已记录错误

                        # 检查图片是否成功处理（使用sample.id查找）
                        if sample.id not in image_paths:
                            error_msg = f"Image not available for sample {sample.id}"
                            format_errors.append(error_msg)
                            progress.update(task, advance=1)
                            continue

                        formatted = sample_formats[sample.id]

                        # 构造ShareGPT格式
                        item = {
                            "instruction": formatted["instruction"],
                            "input": "",  # 根据设计要求为空
                            "output": formatted["output"],
                            "images": [image_paths[sample.id]],  # 使用sample.id查找
                        }

                        sharegpt_data.append(item)

                        # 更新进度
                        progress.update(
                            task,
                            advance=1,
                            description=f"Building output... ({i + 1}/{len(samples)})",
                        )

                    except Exception as e:
                        # 记录错误但继续处理其他样本
                        error_msg = (
                            f"Failed to build output for sample {sample.id}: {e}"
                        )
                        format_errors.append(error_msg)
                        console.print(f"[yellow]Warning: {error_msg}[/yellow]")
                        progress.update(task, advance=1)
                        continue
        else:
            # 不显示进度条的版本
            for sample in samples:
                try:
                    # 检查样本格式化是否成功
                    if sample.id not in sample_formats:
                        continue  # 格式化失败，已记录错误

                    # 检查图片是否成功处理（使用sample.id查找）
                    if sample.id not in image_paths:
                        error_msg = f"Image not available for sample {sample.id}"
                        format_errors.append(error_msg)
                        continue

                    formatted = sample_formats[sample.id]

                    # 构造ShareGPT格式
                    item = {
                        "instruction": formatted["instruction"],
                        "input": "",  # 根据设计要求为空
                        "output": formatted["output"],
                        "images": [image_paths[sample.id]],  # 使用sample.id查找
                    }

                    sharegpt_data.append(item)

                except Exception as e:
                    # 记录错误但继续处理其他样本
                    error_msg = f"Failed to build output for sample {sample.id}: {e}"
                    format_errors.append(error_msg)
                    continue

        if show_progress:
            console.print(
                f"[green]✓[/green] Successfully processed {len(sharegpt_data)} samples"
            )

        # 合并所有错误
        all_errors = copy_errors + format_errors
        return sharegpt_data, all_errors

    def copy_images_with_processing(
        self,
        samples: List[Sample],
        export_dir: Path,
        image_processors: Dict[
            int, Callable[[PILImage.Image], PILImage.Image]
        ],  # sample_id -> image_processor
        image_subdir: str = "images",
        console: Optional[Console] = None,
        show_progress: bool = True,
    ) -> Tuple[Dict[int, str], List[str]]:
        """
        复制并处理导出相关的图片到指定目录

        现在支持每个样本独立的图像处理，即使多个样本共享同一图像，
        每个样本都会得到自己的处理后图像副本。

        Args:
            samples: 样本列表
            export_dir: 导出根目录
            image_processors: 样本ID到图像处理函数的映射
            image_subdir: 图片子目录名
            console: Rich Console对象，用于显示进度

        Returns:
            Tuple[Dict[int, str], List[str]]: (样本ID到新路径的映射, 错误信息列表)
        """
        if console is None:
            console = Console()

        # 创建图片目录（线程安全）
        images_dir = export_dir / image_subdir
        with self._dir_creation_lock:
            images_dir.mkdir(parents=True, exist_ok=True)

        # 收集需要处理的样本任务（每个样本独立处理）
        sample_tasks = []  # [(sample, image, needs_processing)]
        for sample in samples:
            needs_processing = sample.id in image_processors
            sample_tasks.append((sample, sample.image, needs_processing))

        if show_progress:
            processing_count = sum(
                1 for _, _, needs_processing in sample_tasks if needs_processing
            )
            console.print(
                f"[blue]📷 Found {len(sample_tasks)} samples, {processing_count} need image processing[/blue]"
            )

        # 处理图片并生成新路径映射
        sample_image_paths = {}  # sample_id -> 新的相对路径
        errors = []

        # 获取最大工作线程数
        max_workers = min(settings.EXPORT_MAX_WORKERS, len(sample_tasks))
        if show_progress:
            console.print(
                f"[blue]🧵 Using {max_workers} worker threads for per-sample image processing[/blue]"
            )

        def process_single_sample(sample_task_item):
            """处理单个样本的图像函数（每个样本独立处理）"""
            sample, image, needs_processing = sample_task_item

            try:
                # 获取源文件路径
                source_path = self.image_service.get_absolute_path(image)

                if not source_path.exists():
                    return (
                        sample.id,  # 返回sample_id而不是image_id
                        None,
                        f"Source image not found: {source_path}",
                    )

                # 检查是否需要图像处理
                if needs_processing:
                    # 获取该样本的图像处理函数
                    image_processor = image_processors[sample.id]

                    # 解析软链接获取真实文件
                    real_source_path = (
                        source_path.resolve()
                        if source_path.is_symlink()
                        else source_path
                    )

                    # 处理图像
                    processed_image = self.process_image_with_formatter(
                        real_source_path, image_processor
                    )

                    if processed_image is None:
                        return (
                            sample.id,  # 返回sample_id
                            None,
                            f"Failed to process image {image.hash_value} for sample {sample.id}",
                        )

                    # 保存处理后的图像 - 使用样本特定的文件名
                    original_extension = source_path.suffix or ".jpg"
                    target_filename = f"{image.hash_value}_sample_{sample.id}_processed{original_extension}"
                    target_path = images_dir / target_filename

                    # 防止文件名冲突的安全检查
                    counter = 1
                    while target_path.exists():
                        target_filename = f"{image.hash_value}_sample_{sample.id}_processed_{counter}{original_extension}"
                        target_path = images_dir / target_filename
                        counter += 1

                    try:
                        processed_image.save(target_path, quality=95)
                    except Exception as e:
                        return (
                            sample.id,  # 返回sample_id
                            None,
                            f"Failed to save processed image {image.hash_value} for sample {sample.id}: {str(e)}",
                        )

                else:
                    # 不需要处理，直接复制（使用样本特定的文件名以避免冲突）
                    original_extension = source_path.suffix
                    target_filename = (
                        f"{image.hash_value}_sample_{sample.id}{original_extension}"
                    )
                    target_path = images_dir / target_filename

                    # 防止文件名冲突的安全检查
                    counter = 1
                    while target_path.exists():
                        # 先验证文件完整性
                        if self._verify_file_integrity(source_path, target_path):
                            # 文件已存在且完整，跳过复制
                            relative_path = Path(image_subdir) / target_filename
                            return sample.id, str(relative_path), None  # 返回sample_id
                        else:
                            # 文件存在但不完整，生成新文件名
                            target_filename = f"{image.hash_value}_sample_{sample.id}_{counter}{original_extension}"
                            target_path = images_dir / target_filename
                            counter += 1

                    # 复制文件
                    try:
                        # 解析软链接获取真实文件
                        real_source_path = (
                            source_path.resolve()
                            if source_path.is_symlink()
                            else source_path
                        )
                        shutil.copy2(real_source_path, target_path)

                        # 验证复制后的文件完整性
                        if not self._verify_file_integrity(
                            real_source_path, target_path
                        ):
                            if target_path.exists():
                                target_path.unlink()
                            return (
                                sample.id,  # 返回sample_id
                                None,
                                f"Copy verification failed for image {image.hash_value} for sample {sample.id}",
                            )

                    except Exception as e:
                        return (
                            sample.id,  # 返回sample_id
                            None,
                            f"Failed to copy image {image.hash_value} for sample {sample.id}: {str(e)}",
                        )

                # 记录新路径（相对于导出目录的路径）
                relative_path = Path(image_subdir) / target_filename
                return sample.id, str(relative_path), None  # 返回sample_id

            except Exception as e:
                return (
                    sample.id,  # 返回sample_id
                    None,
                    f"Unexpected error processing sample {sample.id} with image {sample.image_id}: {str(e)}",
                )

        # 使用线程池并行处理样本图像
        if show_progress:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                MofNCompleteColumn(),
                TimeRemainingColumn(),
                console=console,
            ) as progress:
                task = progress.add_task(
                    "Processing samples...", total=len(sample_tasks)
                )

                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 提交所有任务
                    future_to_sample = {
                        executor.submit(process_single_sample, item): item[0]
                        for item in sample_tasks
                    }

                    # 收集结果
                    completed = 0
                    for future in as_completed(future_to_sample):
                        sample = future_to_sample[future]
                        try:
                            result_sample_id, relative_path, error = future.result()

                            if error:
                                errors.append(error)
                                console.print(f"[yellow]Warning: {error}[/yellow]")
                            elif relative_path:
                                sample_image_paths[result_sample_id] = relative_path

                            completed += 1
                            progress.update(
                                task,
                                advance=1,
                                description=f"Processing samples... ({completed}/{len(sample_tasks)})",
                            )

                        except Exception as e:
                            error_msg = f"Thread execution error for sample {sample.id}: {str(e)}"
                            errors.append(error_msg)
                            console.print(f"[red]Error: {error_msg}[/red]")
                            completed += 1
                            progress.update(task, advance=1)
        else:
            # 不显示进度条的版本
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_sample = {
                    executor.submit(process_single_sample, item): item[0]
                    for item in sample_tasks
                }

                # 收集结果
                for future in as_completed(future_to_sample):
                    sample = future_to_sample[future]
                    try:
                        result_sample_id, relative_path, error = future.result()

                        if error:
                            errors.append(error)
                        elif relative_path:
                            sample_image_paths[result_sample_id] = relative_path

                    except Exception as e:
                        error_msg = (
                            f"Thread execution error for sample {sample.id}: {str(e)}"
                        )
                        errors.append(error_msg)

        success_count = len(sample_image_paths)
        if show_progress:
            if errors:
                console.print(
                    f"[yellow]⚠️ Processed {success_count}/{len(sample_tasks)} samples with {len(errors)} errors[/yellow]"
                )
            else:
                console.print(
                    f"[green]✓[/green] Successfully processed all {success_count} samples"
                )

        return sample_image_paths, errors

    def _align_samples_to_batch_size(
        self,
        train_samples: List[Sample],
        val_samples: List[Sample],
        test_samples: List[Sample],
        batch_size: int,
        random_seed: Optional[int] = None,
    ) -> Tuple[List[Sample], List[Sample], List[Sample]]:
        """
        调整样本列表以满足批次大小约束

        Args:
            train_samples: 训练样本列表
            val_samples: 验证样本列表
            test_samples: 测试样本列表
            batch_size: 批次大小
            random_seed: 随机种子

        Returns:
            Tuple[List[Sample], List[Sample], List[Sample]]: 调整后的样本列表
        """
        if random_seed is not None:
            random.seed(random_seed)

        # 计算每个数据集需要调整到的大小（批次大小的倍数）
        train_aligned_size = (len(train_samples) // batch_size) * batch_size
        val_aligned_size = (len(val_samples) // batch_size) * batch_size
        test_aligned_size = (len(test_samples) // batch_size) * batch_size

        # 收集被丢弃的样本
        dropped_samples = []
        dropped_samples.extend(train_samples[train_aligned_size:])
        dropped_samples.extend(val_samples[val_aligned_size:])
        dropped_samples.extend(test_samples[test_aligned_size:])

        # 调整样本列表大小
        train_aligned = train_samples[:train_aligned_size]
        val_aligned = val_samples[:val_aligned_size]
        test_aligned = test_samples[:test_aligned_size]

        # 如果有足够的丢弃样本组成新的批次，尝试重新分配
        if len(dropped_samples) >= batch_size:
            random.shuffle(dropped_samples)

            # 按原始比例重新分配丢弃的样本
            total_original = len(train_samples) + len(val_samples) + len(test_samples)
            train_ratio = len(train_samples) / total_original
            val_ratio = len(val_samples) / total_original

            available_batches = len(dropped_samples) // batch_size

            # 计算每个数据集应该获得多少个完整批次
            train_extra_batches = int(available_batches * train_ratio)
            val_extra_batches = int(available_batches * val_ratio)
            test_extra_batches = (
                available_batches - train_extra_batches - val_extra_batches
            )

            # 分配额外的批次
            idx = 0
            for _ in range(train_extra_batches):
                train_aligned.extend(dropped_samples[idx : idx + batch_size])
                idx += batch_size
            for _ in range(val_extra_batches):
                val_aligned.extend(dropped_samples[idx : idx + batch_size])
                idx += batch_size
            for _ in range(test_extra_batches):
                test_aligned.extend(dropped_samples[idx : idx + batch_size])
                idx += batch_size

        return train_aligned, val_aligned, test_aligned

    def split_samples_by_mode(
        self,
        samples: List[Sample],
        split_ratios: List[int],
        random_seed: Optional[int] = None,
        batch_size: Optional[int] = None,
    ) -> Tuple[List[Sample], List[Sample], List[Sample]]:
        """
        按模式进行分层抽样，将样本划分为训练集、验证集和测试集

        Args:
            samples: 要划分的样本列表
            split_ratios: 划分比例，例如 [8, 1, 1] 表示 8:1:1
            random_seed: 随机种子，确保可重现性
            batch_size: 训练批次大小，确保各数据集样本数为此值的倍数

        Returns:
            Tuple[List[Sample], List[Sample], List[Sample]]: (训练集, 验证集, 测试集)
        """
        if random_seed is not None:
            random.seed(random_seed)

        # 按模式分组样本
        samples_by_mode = defaultdict(list)
        for sample in samples:
            samples_by_mode[sample.mode].append(sample)

        # 计算总比例
        total_ratio = sum(split_ratios)
        train_ratio = split_ratios[0] / total_ratio
        val_ratio = split_ratios[1] / total_ratio

        train_samples = []
        val_samples = []
        test_samples = []

        # 对每个模式进行分层抽样
        for mode, mode_samples in samples_by_mode.items():
            # 打乱样本顺序
            mode_samples_shuffled = mode_samples.copy()
            random.shuffle(mode_samples_shuffled)

            total_count = len(mode_samples_shuffled)
            train_count = int(total_count * train_ratio)
            val_count = int(total_count * val_ratio)

            # 分配样本
            train_samples.extend(mode_samples_shuffled[:train_count])
            val_samples.extend(
                mode_samples_shuffled[train_count : train_count + val_count]
            )
            test_samples.extend(mode_samples_shuffled[train_count + val_count :])

        # 如果指定了批次大小，调整最终结果以满足批次对齐要求
        if batch_size is not None and batch_size > 0:
            train_samples, val_samples, test_samples = (
                self._align_samples_to_batch_size(
                    train_samples, val_samples, test_samples, batch_size, random_seed
                )
            )

        return train_samples, val_samples, test_samples

    def stratify_samples_by_batch(
        self,
        samples: List[Sample],
        target_total_count: int,
        random_seed: Optional[int] = None,
    ) -> List[Sample]:
        """
        按批次进行分层抽样，保持原有批次间的比例关系

        Args:
            samples: 要抽样的样本列表
            target_total_count: 目标总样本数量
            random_seed: 随机种子，确保可重现性

        Returns:
            List[Sample]: 分层抽样后的样本列表
        """
        if random_seed is not None:
            random.seed(random_seed)

        # 按批次分组样本
        samples_by_batch = defaultdict(list)
        for sample in samples:
            samples_by_batch[sample.batch_id].append(sample)

        if not samples_by_batch:
            return []

        total_samples = len(samples)
        if target_total_count >= total_samples:
            # 如果目标数量大于等于总数量，返回所有样本
            shuffled_samples = samples.copy()
            random.shuffle(shuffled_samples)
            return shuffled_samples

        stratified_samples = []

        # 对每个批次按比例进行抽样
        for batch_id, batch_samples in samples_by_batch.items():
            # 计算该批次应该抽取的样本数量（按比例）
            batch_ratio = len(batch_samples) / total_samples
            target_batch_count = int(target_total_count * batch_ratio)

            # 确保至少抽取1个样本（如果该批次有样本的话）
            if target_batch_count == 0 and len(batch_samples) > 0:
                target_batch_count = 1

            # 打乱样本顺序并抽取
            batch_samples_shuffled = batch_samples.copy()
            random.shuffle(batch_samples_shuffled)

            selected_samples = batch_samples_shuffled[:target_batch_count]
            stratified_samples.extend(selected_samples)

        # 最终打乱所有样本
        random.shuffle(stratified_samples)

        return stratified_samples

    def split_samples_by_batch(
        self,
        samples: List[Sample],
        split_ratios: List[int],
        random_seed: Optional[int] = None,
        batch_size: Optional[int] = None,
    ) -> Tuple[List[Sample], List[Sample], List[Sample]]:
        """
        按批次进行分层抽样，将样本划分为训练集、验证集和测试集

        Args:
            samples: 要划分的样本列表
            split_ratios: 划分比例，例如 [8, 1, 1] 表示 8:1:1
            random_seed: 随机种子，确保可重现性
            batch_size: 训练批次大小，确保各数据集样本数为此值的倍数

        Returns:
            Tuple[List[Sample], List[Sample], List[Sample]]: (训练集, 验证集, 测试集)
        """
        if random_seed is not None:
            random.seed(random_seed)

        # 按批次分组样本
        samples_by_batch = defaultdict(list)
        for sample in samples:
            samples_by_batch[sample.batch_id].append(sample)

        # 计算总比例
        total_ratio = sum(split_ratios)
        train_ratio = split_ratios[0] / total_ratio
        val_ratio = split_ratios[1] / total_ratio

        train_samples = []
        val_samples = []
        test_samples = []

        # 对每个批次进行分层抽样
        for batch_id, batch_samples in samples_by_batch.items():
            # 打乱样本顺序
            batch_samples_shuffled = batch_samples.copy()
            random.shuffle(batch_samples_shuffled)

            total_count = len(batch_samples_shuffled)
            train_count = int(total_count * train_ratio)
            val_count = int(total_count * val_ratio)

            # 分配样本
            train_samples.extend(batch_samples_shuffled[:train_count])
            val_samples.extend(
                batch_samples_shuffled[train_count : train_count + val_count]
            )
            test_samples.extend(batch_samples_shuffled[train_count + val_count :])

        # 如果指定了批次大小，调整最终结果以满足批次对齐要求
        if batch_size is not None and batch_size > 0:
            train_samples, val_samples, test_samples = (
                self._align_samples_to_batch_size(
                    train_samples, val_samples, test_samples, batch_size, random_seed
                )
            )

        return train_samples, val_samples, test_samples

    def split_samples_by_mode_and_batch(
        self,
        samples: List[Sample],
        split_ratios: List[int],
        random_seed: Optional[int] = None,
        batch_size: Optional[int] = None,
    ) -> Tuple[List[Sample], List[Sample], List[Sample]]:
        """
        按模式和批次进行分层抽样，将样本划分为训练集、验证集和测试集
        默认使用批次分层抽样（保持批次间的原有比例）

        Args:
            samples: 要划分的样本列表
            split_ratios: 划分比例，例如 [8, 1, 1] 表示 8:1:1
            random_seed: 随机种子，确保可重现性
            batch_size: 训练批次大小，确保各数据集样本数为此值的倍数

        Returns:
            Tuple[List[Sample], List[Sample], List[Sample]]: (训练集, 验证集, 测试集)
        """
        if random_seed is not None:
            random.seed(random_seed)

        # 默认使用批次分层抽样：保持批次间的原有比例，在每个批次内按模式分层
        return self.split_samples_by_batch(
            samples, split_ratios, random_seed, batch_size
        )

    def export_with_filters_and_stratification(
        self,
        batch_ids: Optional[List[int]] = None,
        modes: Optional[List[SampleMode]] = None,
        labels: Optional[List[str]] = None,
        formatter: Optional[str] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        random_seed: Optional[int] = None,
        console: Optional[Console] = None,
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        根据过滤条件导出数据，使用批次分层抽样，并显示进度

        Args:
            batch_ids: 批次ID列表过滤
            modes: 模式列表过滤
            labels: 标签过滤
            formatter: 自定义格式化器名称
            limit: 导出数量限制，None表示全部导出
            offset: 偏移量
            copy_images: 是否复制图片到导出目录
            export_dir: 导出目录（copy_images为True时必需）
            image_subdir: 图片子目录名
            random_seed: 随机种子，确保可重现性
            console: Rich Console对象，用于显示进度

        Returns:
            Tuple[List[Dict[str, Any]], List[str]]: (ShareGPT格式的导出数据, 错误信息列表)
        """
        if console is None:
            console = Console()

        # 先获取总数量用于进度显示
        console.print("[blue]🔍 Counting samples...[/blue]")
        total_count = self.sample_service.count_samples_with_filters(
            batch_ids=batch_ids, modes=modes, labels=labels
        )

        if total_count == 0:
            return [], []

        console.print(f"[blue]📊 Found {total_count} total samples[/blue]")

        # 获取符合条件的样本
        samples = self.sample_service.get_samples_with_filters(
            batch_ids=batch_ids, modes=modes, labels=labels, limit=limit, offset=offset
        )

        if not samples:
            return [], []

        # 如果有多个批次且指定了limit，使用分层抽样
        if limit is not None and len(samples) > limit:
            console.print(
                "[blue]📊 Applying batch stratification for sampling...[/blue]"
            )
            samples = self.stratify_samples_by_batch(
                samples, target_total_count=limit, random_seed=random_seed
            )
            console.print(f"[blue]✅ Stratified to {len(samples)} samples[/blue]")

        # 格式化输出
        return self.format_sharegpt_output_with_progress(
            samples, formatter, copy_images, export_dir, image_subdir, console
        )

    def export_with_split(
        self,
        batch_ids: Optional[List[int]] = None,
        modes: Optional[List[SampleMode]] = None,
        labels: Optional[List[str]] = None,
        formatter: Optional[str] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        split_ratios: List[int] = [8, 1, 1],
        random_seed: Optional[int] = None,
        batch_size: Optional[int] = None,
        console: Optional[Console] = None,
    ) -> Tuple[Dict[str, List[Dict[str, Any]]], List[str]]:
        """
        导出数据并按模式和批次进行分层抽样划分为训练集、验证集和测试集

        Args:
            batch_ids: 批次ID列表过滤
            modes: 模式列表过滤
            labels: 标签过滤
            formatter: 自定义格式化器名称
            limit: 导出数量限制，None表示全部导出
            offset: 偏移量
            copy_images: 是否复制图片到导出目录
            export_dir: 导出目录
            image_subdir: 图片子目录名
            split_ratios: 划分比例，例如 [8, 1, 1]
            random_seed: 随机种子
            batch_size: 训练批次大小，确保各数据集样本数为此值的倍数
            console: Rich Console对象，用于显示进度

        Returns:
            Tuple[Dict[str, List[Dict[str, Any]]], List[str]]: (包含train/val/test的字典, 错误信息列表)
        """
        if console is None:
            console = Console()

        # 先获取所有符合条件的样本
        console.print("[blue]🔍 Fetching samples for splitting...[/blue]")
        samples = self.sample_service.get_samples_with_filters(
            batch_ids=batch_ids, modes=modes, labels=labels, limit=limit, offset=offset
        )

        if not samples:
            return {"train": [], "val": [], "test": []}, []

        console.print(f"[blue]📊 Found {len(samples)} samples to split[/blue]")

        if batch_size is not None and batch_size > 0:
            console.print(f"[blue]🎯 Using batch size constraint: {batch_size}[/blue]")
            console.print(
                "[blue]📐 Ensuring train/val/test sets have sample counts as multiples of batch size[/blue]"
            )

        console.print(
            "[blue]📊 Batch stratification enabled (proportional sampling)[/blue]"
        )

        # 按模式和批次进行分层抽样划分
        console.print(
            "[blue]🎯 Performing stratified sampling by mode and batch...[/blue]"
        )
        train_samples, val_samples, test_samples = self.split_samples_by_mode_and_batch(
            samples, split_ratios, random_seed, batch_size
        )

        # 显示划分统计
        console.print(
            f"[blue]📈 Split results: Train={len(train_samples)}, Val={len(val_samples)}, Test={len(test_samples)}[/blue]"
        )

        # 如果使用了批次大小约束，显示对齐信息
        if batch_size is not None and batch_size > 0:
            train_batches = len(train_samples) // batch_size
            val_batches = len(val_samples) // batch_size
            test_batches = len(test_samples) // batch_size
            total_used = len(train_samples) + len(val_samples) + len(test_samples)
            total_original = len(samples)

            console.print(
                f"[blue]🎯 Batch alignment: Train={train_batches} batches, Val={val_batches} batches, Test={test_batches} batches[/blue]"
            )

            if total_used < total_original:
                dropped = total_original - total_used
                console.print(
                    f"[yellow]⚠️  Note: {dropped} samples dropped to ensure batch size alignment[/yellow]"
                )

        # 分别格式化每个数据集
        all_errors = []
        result = {}

        for split_name, split_samples in [
            ("train", train_samples),
            ("val", val_samples),
            ("test", test_samples),
        ]:
            if split_samples:
                console.print(
                    f"[blue]🔄 Processing {split_name} set ({len(split_samples)} samples)...[/blue]"
                )
                formatted_data, errors = self.format_sharegpt_output_with_progress(
                    split_samples,
                    formatter,
                    copy_images,
                    export_dir,
                    image_subdir,
                    console,
                )
                result[split_name] = formatted_data
                all_errors.extend(errors)
            else:
                result[split_name] = []

        return result, all_errors

    def export_with_filters_mapreduce(
        self,
        batch_ids: Optional[List[int]] = None,
        modes: Optional[List[SampleMode]] = None,
        labels: Optional[List[str]] = None,
        formatter: Optional[str] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        chunk_size: int = 1000,
        console: Optional[Console] = None,
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        使用Map-Reduce模式导出数据，适用于大规模数据集

        Args:
            batch_ids: 批次ID列表过滤
            modes: 模式列表过滤
            labels: 标签过滤
            formatter: 自定义格式化器名称
            limit: 导出数量限制，None表示全部导出
            offset: 偏移量
            copy_images: 是否复制图片到导出目录
            export_dir: 导出目录（copy_images为True时必需）
            image_subdir: 图片子目录名
            chunk_size: 每个处理块的大小
            console: Rich Console对象，用于显示进度

        Returns:
            Tuple[List[Dict[str, Any]], List[str]]: (ShareGPT格式的导出数据, 错误信息列表)
        """
        if console is None:
            console = Console()

        # 获取总数量
        console.print("[blue]🔍 Counting samples...[/blue]")
        total_count = self.sample_service.count_samples_with_filters(
            batch_ids=batch_ids, modes=modes, labels=labels
        )

        if total_count == 0:
            return [], []

        # 确定实际导出数量
        actual_export_count = (
            min(total_count - offset, limit)
            if limit is not None
            else (total_count - offset)
        )

        if actual_export_count <= 0:
            return [], []

        # Note: Map-Reduce info is printed by CLI, not here to avoid duplication

        all_export_data = []
        all_errors = []
        processed_count = 0

        # 计算总块数
        total_chunks = (actual_export_count + chunk_size - 1) // chunk_size

        # 预先收集格式化结果和图像处理器
        sample_formats = {}
        unified_image_paths = {}
        image_copy_errors = []

        if copy_images:
            if export_dir is None:
                raise ValueError("export_dir is required when copy_images=True")

            console.print(
                "[blue]🔄 Pre-scanning samples and formatters for image processors...[/blue]"
            )
            # 获取所有样本以收集唯一图片和图像处理器
            all_samples = self.sample_service.get_samples_with_filters(
                batch_ids=batch_ids,
                modes=modes,
                labels=labels,
                limit=limit,
                offset=offset,
            )

            # 预先应用格式化器以收集图像处理器
            sample_formats, image_processors = self._prescan_formatters_for_processors(
                all_samples, formatter, console
            )

            # 收集唯一图片
            unique_images = {}  # image_id -> Image对象
            for sample in all_samples:
                if sample.image_id not in unique_images:
                    unique_images[sample.image_id] = sample.image

            console.print(
                f"[blue]📷 Found {len(unique_images)} unique images to copy[/blue]"
            )
            if image_processors:
                console.print(
                    f"[blue]🎨 Found {len(image_processors)} samples with image processors[/blue]"
                )

            # 使用统一的进度条复制所有图片（支持图像处理）
            unified_image_paths, image_copy_errors = (
                self._copy_images_unified_with_processing(
                    unique_images,
                    all_samples,
                    image_processors,
                    export_dir,
                    image_subdir,
                    console,
                )
            )
            all_errors.extend(image_copy_errors)

        # 分块处理样本
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            console=console,
        ) as progress:
            export_task = progress.add_task(
                "Exporting samples...", total=actual_export_count
            )

            current_offset = offset
            chunk_num = 0

            while processed_count < actual_export_count:
                chunk_num += 1

                # 计算当前块的大小
                current_chunk_size = min(
                    chunk_size, actual_export_count - processed_count
                )

                # Map阶段：获取并处理当前块
                chunk_data, chunk_errors = self._map_process_export_chunk(
                    batch_ids=batch_ids,
                    modes=modes,
                    labels=labels,
                    formatter=formatter,
                    limit=current_chunk_size,
                    offset=current_offset,
                    copy_images=copy_images,
                    export_dir=export_dir,
                    image_subdir=image_subdir,
                    console=console,
                    unified_image_paths=unified_image_paths if copy_images else None,
                    prescanned_formats=sample_formats if copy_images else None,
                )

                # Reduce阶段：聚合结果
                all_export_data.extend(chunk_data)
                all_errors.extend(chunk_errors)

                # 更新进度
                processed_count += len(chunk_data)
                current_offset += current_chunk_size

                # 更新进度条
                progress.update(
                    export_task,
                    completed=processed_count,
                    description=f"Exporting samples... (chunk {chunk_num}/{total_chunks})",
                )

                # 安全检查：如果没有获取到数据，避免无限循环
                if len(chunk_data) == 0:
                    break

        return all_export_data, all_errors

    def _copy_images_unified(
        self,
        unique_images: Dict[int, Any],  # image_id -> Image对象
        export_dir: Path,
        image_subdir: str = "images",
        console: Optional[Console] = None,
    ) -> Tuple[Dict[int, str], List[str]]:
        """
        统一复制所有唯一图片，使用单一进度条

        Args:
            unique_images: 图片ID到Image对象的映射
            export_dir: 导出根目录
            image_subdir: 图片子目录名
            console: Rich Console对象，用于显示进度

        Returns:
            Tuple[Dict[int, str], List[str]]: (图片ID到新路径的映射, 错误信息列表)
        """
        if console is None:
            console = Console()

        # 创建图片目录
        images_dir = export_dir / image_subdir
        images_dir.mkdir(parents=True, exist_ok=True)

        # 复制图片并生成新路径映射
        image_paths = {}  # image_id -> 新的相对路径
        errors = []

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("Copying images...", total=len(unique_images))

            for i, (image_id, image) in enumerate(unique_images.items()):
                try:
                    # 获取源文件路径
                    source_path = self.image_service.get_absolute_path(image)

                    if not source_path.exists():
                        error_msg = f"Source image not found: {source_path}"
                        errors.append(error_msg)
                        console.print(f"[yellow]Warning: {error_msg}[/yellow]")
                        progress.update(task, advance=1)
                        continue

                    # 保持原有的哈希命名
                    # 从原文件路径获取扩展名
                    original_extension = source_path.suffix
                    target_filename = f"{image.hash_value}{original_extension}"
                    target_path = images_dir / target_filename

                    # 检查是否需要复制（增量复制）
                    if target_path.exists():
                        # 验证文件完整性
                        if self._verify_file_integrity(source_path, target_path):
                            # 文件已存在且完整，跳过复制
                            relative_path = Path(image_subdir) / target_filename
                            image_paths[image_id] = str(relative_path)
                            progress.update(
                                task,
                                advance=1,
                                description=f"Copying images... ({i + 1}/{len(unique_images)}) - skipped (exists)",
                            )
                            continue
                        else:
                            # 文件存在但不完整，需要重新复制
                            console.print(
                                f"[yellow]File integrity check failed, re-copying: {target_filename}[/yellow]"
                            )

                    # 复制文件
                    try:
                        # 解析软链接获取真实文件
                        real_source_path = (
                            source_path.resolve()
                            if source_path.is_symlink()
                            else source_path
                        )
                        shutil.copy2(real_source_path, target_path)

                        # 验证复制后的文件完整性
                        if not self._verify_file_integrity(
                            real_source_path, target_path
                        ):
                            if target_path.exists():
                                target_path.unlink()
                            error_msg = (
                                f"Copy verification failed for image {image.hash_value}"
                            )
                            errors.append(error_msg)
                            console.print(f"[red]Error: {error_msg}[/red]")
                            progress.update(task, advance=1)
                            continue

                    except Exception as e:
                        error_msg = f"Failed to copy image {image.hash_value}: {str(e)}"
                        errors.append(error_msg)
                        console.print(f"[red]Error: {error_msg}[/red]")
                        progress.update(task, advance=1)
                        continue

                    # 记录新路径（相对于导出目录的路径）
                    relative_path = Path(image_subdir) / target_filename
                    image_paths[image_id] = str(relative_path)

                    progress.update(
                        task,
                        advance=1,
                        description=f"Copying images... ({i + 1}/{len(unique_images)})",
                    )

                except Exception as e:
                    error_msg = (
                        f"Unexpected error processing image {image_id}: {str(e)}"
                    )
                    errors.append(error_msg)
                    console.print(f"[red]Error: {error_msg}[/red]")
                    progress.update(task, advance=1)

        success_count = len(image_paths)
        if errors:
            console.print(
                f"[yellow]⚠️ Copied {success_count}/{len(unique_images)} images with {len(errors)} errors[/yellow]"
            )
        else:
            console.print(
                f"[green]✓[/green] Successfully copied all {success_count} images"
            )

        return image_paths, errors

    def _prescan_formatters_for_processors(
        self,
        samples: List[Sample],
        formatter: Optional[str] = None,
        console: Optional[Console] = None,
    ) -> Tuple[Dict[int, Dict[str, Any]], Dict[int, Any]]:
        """
        预先扫描所有样本的格式化器以收集图像处理器

        Args:
            samples: 样本列表
            formatter: 自定义格式化器名称
            console: Rich Console对象

        Returns:
            Tuple[Dict[int, Dict[str, Any]], Dict[int, Any]]: (样本格式化结果, 图像处理器映射)
        """
        if console is None:
            console = Console()

        sample_formats = {}  # sample_id -> formatted_result
        image_processors = {}  # sample_id -> image_processor_func

        # 如果指定了自定义格式化器，导入并获取函数
        formatter_func = None
        if formatter:
            from . import get_formatter, list_formatters

            formatter_func = get_formatter(formatter)
            if not formatter_func:
                available = list_formatters()
                raise ValueError(
                    f"Custom formatter '{formatter}' not found. Available formatters: {available}"
                )

        # 应用格式化器并收集图像处理器
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("Scanning formatters...", total=len(samples))

            for sample in samples:
                try:
                    # 应用格式化
                    if formatter_func:
                        formatted = self.apply_custom_formatter(sample, formatter_func)
                    else:
                        # 使用默认格式化器
                        default_formatter = self.get_default_formatter(sample.mode)
                        formatted = self.apply_custom_formatter(
                            sample, default_formatter
                        )

                    sample_formats[sample.id] = formatted

                    # 检查是否有图像处理器
                    if "image_processor" in formatted:
                        image_processors[sample.id] = formatted["image_processor"]

                    # 更新进度
                    progress.update(task, advance=1)

                except Exception as e:
                    error_msg = f"Failed to format sample {sample.id}: {e}"
                    console.print(f"[yellow]Warning: {error_msg}[/yellow]")
                    progress.update(task, advance=1)
                    continue

        return sample_formats, image_processors

    def _copy_images_unified_with_processing(
        self,
        unique_images: Dict[int, Any],  # image_id -> Image对象
        all_samples: List[Sample],
        image_processors: Dict[int, Any],  # sample_id -> image_processor_func
        export_dir: Path,
        image_subdir: str = "images",
        console: Optional[Console] = None,
    ) -> Tuple[Dict[int, str], List[str]]:
        """
        统一复制所有样本图片并应用图像处理，使用并行处理和单一进度条

        现在支持每个样本独立的图像处理，即使多个样本共享同一图像，
        每个样本都会得到自己的处理后图像副本。

        Args:
            unique_images: 图片ID到Image对象的映射（用于获取图像信息）
            all_samples: 所有样本列表
            image_processors: 样本ID到图像处理函数的映射
            export_dir: 导出根目录
            image_subdir: 图片子目录名
            console: Rich Console对象，用于显示进度

        Returns:
            Tuple[Dict[int, str], List[str]]: (样本ID到新路径的映射, 错误信息列表)
        """
        if console is None:
            console = Console()

        # 创建图片目录（线程安全）
        images_dir = export_dir / image_subdir
        with self._dir_creation_lock:
            images_dir.mkdir(parents=True, exist_ok=True)

        # 构建样本任务列表（每个样本独立处理）
        sample_tasks = []  # [(sample, image, needs_processing)]
        for sample in all_samples:
            image = unique_images.get(sample.image_id)
            if image is None:
                continue  # 跳过没有图像信息的样本
            needs_processing = sample.id in image_processors
            sample_tasks.append((sample, image, needs_processing))

        processing_count = sum(
            1 for _, _, needs_processing in sample_tasks if needs_processing
        )
        console.print(
            f"[blue]📷 Found {len(sample_tasks)} samples, {processing_count} need image processing[/blue]"
        )

        # 获取最大工作线程数
        max_workers = min(settings.EXPORT_MAX_WORKERS, len(sample_tasks))
        console.print(
            f"[blue]🧵 Using {max_workers} worker threads for per-sample image processing[/blue]"
        )

        # 复制图片并生成新路径映射
        sample_image_paths = {}  # sample_id -> 新的相对路径
        errors = []

        def process_single_sample(
            sample_task_item: Tuple[Sample, Any, bool],
        ) -> Tuple[int, Optional[str], Optional[str]]:
            """
            处理单个样本的图像工作函数（线程安全）
            返回: (sample_id, relative_path_or_None, error_message_or_None)
            """
            sample, image, needs_processing = sample_task_item
            try:
                # 获取源文件路径
                source_path = self.image_service.get_absolute_path(image)

                if not source_path.exists():
                    return (
                        sample.id,  # 返回sample_id
                        None,
                        f"Source image not found: {source_path}",
                    )

                if needs_processing:
                    # 需要处理的样本
                    image_processor = image_processors[sample.id]

                    # 解析软链接获取真实文件
                    real_source_path = (
                        source_path.resolve()
                        if source_path.is_symlink()
                        else source_path
                    )

                    # 处理图像
                    processed_image = self.process_image_with_formatter(
                        real_source_path, image_processor
                    )

                    if processed_image is None:
                        return (
                            sample.id,  # 返回sample_id
                            None,
                            f"Failed to process image {image.hash_value} for sample {sample.id}",
                        )

                    # 保存处理后的图像 - 使用样本特定的文件名
                    original_extension = source_path.suffix or ".jpg"
                    target_filename = f"{image.hash_value}_sample_{sample.id}_processed{original_extension}"
                    target_path = images_dir / target_filename

                    # 防止文件名冲突的安全检查
                    counter = 1
                    while target_path.exists():
                        target_filename = f"{image.hash_value}_sample_{sample.id}_processed_{counter}{original_extension}"
                        target_path = images_dir / target_filename
                        counter += 1

                    try:
                        processed_image.save(target_path, quality=95, optimize=True)
                    except Exception as e:
                        return (
                            sample.id,  # 返回sample_id
                            None,
                            f"Failed to save processed image {image.hash_value} for sample {sample.id}: {str(e)}",
                        )

                    # 返回相对路径
                    relative_path = Path(image_subdir) / target_filename
                    return sample.id, str(relative_path), None  # 返回sample_id

                else:
                    # 不需要处理，直接复制（使用样本特定的文件名以避免冲突）
                    original_extension = source_path.suffix
                    target_filename = (
                        f"{image.hash_value}_sample_{sample.id}{original_extension}"
                    )
                    target_path = images_dir / target_filename

                    # 防止文件名冲突的安全检查
                    counter = 1
                    while target_path.exists():
                        # 先验证文件完整性
                        if self._verify_file_integrity(source_path, target_path):
                            # 文件已存在且完整，跳过复制
                            relative_path = Path(image_subdir) / target_filename
                            return sample.id, str(relative_path), None  # 返回sample_id
                        else:
                            # 文件存在但不完整，生成新文件名
                            target_filename = f"{image.hash_value}_sample_{sample.id}_{counter}{original_extension}"
                            target_path = images_dir / target_filename
                            counter += 1

                    # 复制文件
                    try:
                        # 解析软链接获取真实文件
                        real_source_path = (
                            source_path.resolve()
                            if source_path.is_symlink()
                            else source_path
                        )
                        shutil.copy2(real_source_path, target_path)

                        # 验证复制后的文件完整性
                        if not self._verify_file_integrity(
                            real_source_path, target_path
                        ):
                            if target_path.exists():
                                target_path.unlink()
                            return (
                                sample.id,  # 返回sample_id
                                None,
                                f"Copy verification failed for image {image.hash_value} for sample {sample.id}",
                            )

                    except Exception as e:
                        return (
                            sample.id,  # 返回sample_id
                            None,
                            f"Failed to copy image {image.hash_value} for sample {sample.id}: {str(e)}",
                        )

                    # 返回相对路径
                    relative_path = Path(image_subdir) / target_filename
                    return sample.id, str(relative_path), None  # 返回sample_id

            except Exception as e:
                return (
                    sample.id,  # 返回sample_id
                    None,
                    f"Unexpected error processing sample {sample.id} with image {sample.image_id}: {str(e)}",
                )

        # 使用并行处理
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("Processing samples...", total=len(sample_tasks))

            if len(sample_tasks) <= 5:
                # 小批量使用串行处理避免线程开销
                for sample_task_item in sample_tasks:
                    sample_id, relative_path, error = process_single_sample(
                        sample_task_item
                    )

                    if error:
                        errors.append(error)
                        console.print(f"[yellow]Warning: {error}[/yellow]")
                    elif relative_path:
                        sample_image_paths[sample_id] = relative_path

                    # 更新进度
                    sample, _, needs_processing = sample_task_item
                    status = "processed" if needs_processing else "copied"
                    progress.update(
                        task,
                        advance=1,
                        description=f"Processing samples... ({len(sample_image_paths) + len(errors)}/{len(sample_tasks)}) - {status}",
                    )
            else:
                # 大批量使用并行处理
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 提交所有任务
                    future_to_sample = {
                        executor.submit(process_single_sample, item): item[0]
                        for item in sample_tasks
                    }

                    # 收集结果
                    completed = 0
                    for future in as_completed(future_to_sample):
                        sample = future_to_sample[future]
                        try:
                            result_sample_id, relative_path, error = future.result()

                            if error:
                                errors.append(error)
                                console.print(f"[yellow]Warning: {error}[/yellow]")
                            elif relative_path:
                                sample_image_paths[result_sample_id] = relative_path

                            completed += 1

                            # 确定处理状态（从sample_tasks中查找）
                            needs_processing = sample.id in image_processors
                            status = "processed" if needs_processing else "copied"

                            progress.update(
                                task,
                                advance=1,
                                description=f"Processing samples... ({completed}/{len(sample_tasks)}) - {status}",
                            )

                        except Exception as e:
                            error_msg = f"Thread execution error for sample {sample.id}: {str(e)}"
                            errors.append(error_msg)
                            console.print(f"[red]Error: {error_msg}[/red]")
                            completed += 1
                            progress.update(task, advance=1)

        success_count = len(sample_image_paths)
        processed_count = sum(
            1 for _, _, needs_processing in sample_tasks if needs_processing
        )
        if errors:
            console.print(
                f"[yellow]⚠️ Processed {success_count}/{len(sample_tasks)} samples "
                f"({processed_count} with image processing) with {len(errors)} errors[/yellow]"
            )
        else:
            console.print(
                f"[green]✓[/green] Successfully processed all {success_count} samples "
                f"({processed_count} processed)"
            )

        return sample_image_paths, errors

    def export_with_split_mapreduce(
        self,
        batch_ids: Optional[List[int]] = None,
        modes: Optional[List[SampleMode]] = None,
        labels: Optional[List[str]] = None,
        formatter: Optional[str] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        split_ratios: List[int] = [8, 1, 1],
        random_seed: Optional[int] = None,
        batch_size: Optional[int] = None,
        chunk_size: int = 1000,
        console: Optional[Console] = None,
    ) -> Tuple[Dict[str, List[Dict[str, Any]]], List[str]]:
        """
        使用Map-Reduce模式导出数据并进行分层抽样划分，适用于大规模数据集

        Args:
            batch_ids: 批次ID列表过滤
            modes: 模式列表过滤
            labels: 标签过滤
            formatter: 自定义格式化器名称
            limit: 导出数量限制，None表示全部导出
            offset: 偏移量
            copy_images: 是否复制图片到导出目录
            export_dir: 导出目录
            image_subdir: 图片子目录名
            split_ratios: 划分比例，例如 [8, 1, 1]
            random_seed: 随机种子
            batch_size: 训练批次大小，确保各数据集样本数为此值的倍数
            chunk_size: 每个处理块的大小
            console: Rich Console对象，用于显示进度

        Returns:
            Tuple[Dict[str, List[Dict[str, Any]]], List[str]]: (包含train/val/test的字典, 错误信息列表)
        """
        if console is None:
            console = Console()

        # 获取总数量
        console.print("[blue]🔍 Counting samples for splitting...[/blue]")
        total_count = self.sample_service.count_samples_with_filters(
            batch_ids=batch_ids, modes=modes, labels=labels
        )

        if total_count == 0:
            return {"train": [], "val": [], "test": []}, []

        # 确定实际导出数量
        actual_export_count = (
            min(total_count - offset, limit)
            if limit is not None
            else (total_count - offset)
        )

        if actual_export_count <= 0:
            return {"train": [], "val": [], "test": []}, []

        # Note: Map-Reduce info is printed by CLI, not here to avoid duplication

        if batch_size is not None and batch_size > 0:
            console.print(f"[blue]🎯 Using batch size constraint: {batch_size}[/blue]")

        console.print(
            "[blue]📊 Batch stratification enabled (proportional sampling)[/blue]"
        )

        # 初始化结果容器
        all_samples = []
        all_errors = []
        processed_count = 0

        # 分块获取所有样本（用于分层抽样）
        console.print("[blue]🔄 Collecting samples for stratified splitting...[/blue]")

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            console=console,
        ) as progress:
            collection_task = progress.add_task(
                "Collecting samples...", total=actual_export_count
            )

            current_offset = offset
            while processed_count < actual_export_count:
                # 计算当前块的大小
                current_chunk_size = min(
                    chunk_size, actual_export_count - processed_count
                )

                # 获取当前块的样本
                chunk_samples = self.sample_service.get_samples_with_filters(
                    batch_ids=batch_ids,
                    modes=modes,
                    labels=labels,
                    limit=current_chunk_size,
                    offset=current_offset,
                )

                if not chunk_samples:
                    break

                all_samples.extend(chunk_samples)
                processed_count += len(chunk_samples)
                current_offset += current_chunk_size

                # 更新进度条
                progress.update(collection_task, completed=processed_count)

        if not all_samples:
            return {"train": [], "val": [], "test": []}, []

        # 执行分层抽样划分
        console.print(
            "[blue]🎯 Performing stratified sampling by mode and batch...[/blue]"
        )
        train_samples, val_samples, test_samples = self.split_samples_by_mode_and_batch(
            all_samples, split_ratios, random_seed, batch_size
        )

        # 显示划分统计
        console.print(
            f"[blue]📈 Split results: Train={len(train_samples)}, Val={len(val_samples)}, Test={len(test_samples)}[/blue]"
        )

        # 如果使用了批次大小约束，显示对齐信息
        if batch_size is not None and batch_size > 0:
            train_batches = len(train_samples) // batch_size
            val_batches = len(val_samples) // batch_size
            test_batches = len(test_samples) // batch_size
            total_used = len(train_samples) + len(val_samples) + len(test_samples)
            total_original = len(all_samples)

            console.print(
                f"[blue]🎯 Batch alignment: Train={train_batches} batches, Val={val_batches} batches, Test={test_batches} batches[/blue]"
            )

            if total_used < total_original:
                dropped = total_original - total_used
                console.print(
                    f"[yellow]⚠️  Note: {dropped} samples dropped to ensure batch size alignment[/yellow]"
                )

        # 预先收集格式化结果和图像处理器
        sample_formats = {}
        unified_image_paths = {}
        image_copy_errors = []

        if copy_images:
            if export_dir is None:
                raise ValueError("export_dir is required when copy_images=True")

            console.print(
                "[blue]🔄 Pre-scanning samples and formatters for image processors...[/blue]"
            )

            # 预先应用格式化器以收集图像处理器
            sample_formats, image_processors = self._prescan_formatters_for_processors(
                all_samples, formatter, console
            )

            # 收集所有splits中的唯一图片
            unique_images = {}  # image_id -> Image对象
            for sample in all_samples:
                if sample.image_id not in unique_images:
                    unique_images[sample.image_id] = sample.image

            console.print(
                f"[blue]📷 Found {len(unique_images)} unique images to copy[/blue]"
            )
            if image_processors:
                console.print(
                    f"[blue]🎨 Found {len(image_processors)} samples with image processors[/blue]"
                )

            # 使用统一的进度条复制所有图片（支持图像处理）
            unified_image_paths, image_copy_errors = (
                self._copy_images_unified_with_processing(
                    unique_images,
                    all_samples,
                    image_processors,
                    export_dir,
                    image_subdir,
                    console,
                )
            )
            all_errors.extend(image_copy_errors)

        # 使用Map-Reduce方式分别处理每个数据集
        result = {}

        for split_name, split_samples in [
            ("train", train_samples),
            ("val", val_samples),
            ("test", test_samples),
        ]:
            if split_samples:
                console.print(
                    f"[blue]🔄 Processing {split_name} set ({len(split_samples)} samples) with Map-Reduce...[/blue]"
                )

                # 对每个split使用chunked processing
                split_data, split_errors = self._process_split_samples_mapreduce(
                    split_samples,
                    formatter,
                    copy_images,
                    export_dir,
                    image_subdir,
                    chunk_size,
                    console,
                    unified_image_paths if copy_images else None,
                    sample_formats if copy_images else None,
                )

                result[split_name] = split_data
                all_errors.extend(split_errors)
            else:
                result[split_name] = []

        return result, all_errors

    def _process_split_samples_mapreduce(
        self,
        samples: List[Sample],
        formatter: Optional[str] = None,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        chunk_size: int = 1000,
        console: Optional[Console] = None,
        unified_image_paths: Optional[Dict[int, str]] = None,
        prescanned_formats: Optional[Dict[int, Dict[str, Any]]] = None,
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        使用Map-Reduce方式处理单个split的样本，使用统一的进度条
        """
        if not samples:
            return [], []

        if console is None:
            console = Console()

        all_formatted_data = []
        all_errors = []

        # 计算总块数
        total_chunks = (len(samples) + chunk_size - 1) // chunk_size

        # 使用统一的进度条
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("Processing samples...", total=len(samples))

            # 分块处理样本
            for i in range(0, len(samples), chunk_size):
                chunk_samples = samples[i : i + chunk_size]
                chunk_num = i // chunk_size + 1

                # 使用内部方法处理这个块（不显示进度条）
                chunk_data, chunk_errors = self._format_sharegpt_output_internal(
                    chunk_samples,
                    formatter,
                    copy_images,
                    export_dir,
                    image_subdir,
                    console,
                    show_progress=False,  # 不显示单独的进度条
                    unified_image_paths=unified_image_paths,
                    prescanned_formats=prescanned_formats,
                )

                all_formatted_data.extend(chunk_data)
                all_errors.extend(chunk_errors)

                # 更新统一的进度条
                progress.update(
                    task,
                    advance=len(chunk_samples),
                    description=f"Processing samples... (chunk {chunk_num}/{total_chunks})",
                )

        return all_formatted_data, all_errors

    def _map_process_export_chunk(
        self,
        batch_ids: Optional[List[int]] = None,
        modes: Optional[List[SampleMode]] = None,
        labels: Optional[List[str]] = None,
        formatter: Optional[str] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        console: Optional[Console] = None,
        unified_image_paths: Optional[Dict[int, str]] = None,
        prescanned_formats: Optional[Dict[int, Dict[str, Any]]] = None,
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        Map阶段：处理单个导出块，不显示单独的进度条
        """
        # 获取当前块的样本
        samples = self.sample_service.get_samples_with_filters(
            batch_ids=batch_ids, modes=modes, labels=labels, limit=limit, offset=offset
        )

        if not samples:
            return [], []

        # 使用内部方法处理这个块（不显示进度条）
        return self._format_sharegpt_output_internal(
            samples,
            formatter,
            copy_images,
            export_dir,
            image_subdir,
            console,
            show_progress=False,
            unified_image_paths=unified_image_paths,
            prescanned_formats=prescanned_formats,
        )
