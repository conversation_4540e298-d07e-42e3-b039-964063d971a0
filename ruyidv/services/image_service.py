import hashlib
from pathlib import Path
from typing import Optional, Tuple

from PIL import Image as PILImage
from sqlalchemy.orm import Session

from ..config import settings
from ..models import Image


class ImageService:
    """图片管理服务"""

    def __init__(self, db: Session):
        self.db = db
        self.storage_root = settings.IMAGES_DIR
        self.allowed_extensions = settings.ALLOWED_IMAGE_EXTENSIONS
        self.max_file_size = settings.MAX_FILE_SIZE

    def calculate_image_hash(self, file_path: Path) -> str:
        """计算图片文件的SHA256哈希值"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()

    def check_duplicate_image(self, hash_value: str) -> Optional[Image]:
        """检查图片是否已存在，返回已存在的图片记录或None"""
        return self.db.query(Image).filter(Image.hash_value == hash_value).first()

    def get_image_info(self, file_path: Path) -> Tuple[int, int, int]:
        """获取图片基本信息：宽度、高度、文件大小"""
        file_size = file_path.stat().st_size

        try:
            with PILImage.open(file_path) as img:
                width, height = img.size
        except Exception:
            # 如果无法读取图片信息，返回默认值
            width, height = 0, 0

        return width, height, file_size

    def organize_image_path(self, hash_value: str, original_filename: str) -> Path:
        """根据哈希值组织存储路径结构"""
        # 获取文件扩展名
        extension = Path(original_filename).suffix.lower()
        if not extension:
            extension = ".jpg"  # 默认扩展名

        # 创建分层目录结构: hash[:2]/hash[2:4]/hash.ext
        dir_level1 = hash_value[:2]
        dir_level2 = hash_value[2:4]

        target_dir = self.storage_root / dir_level1 / dir_level2
        target_dir.mkdir(parents=True, exist_ok=True)

        return target_dir / f"{hash_value}{extension}"

    def validate_image_format(self, file_path: Path) -> bool:
        """验证图片格式和大小"""
        # 检查文件扩展名
        if file_path.suffix.lower() not in self.allowed_extensions:
            return False

        # 检查文件大小
        file_size = file_path.stat().st_size
        if file_size > self.max_file_size:
            return False

        # 尝试打开图片验证格式
        try:
            with PILImage.open(file_path) as img:
                img.verify()
            return True
        except Exception:
            return False

    def save_image_to_storage(
        self,
        source_path: Path,
        original_filename: str,
        use_symlinks: Optional[bool] = None,
    ) -> Image:
        """
        将图片保存到存储目录并创建数据库记录

        Args:
            source_path: 源文件路径
            original_filename: 原始文件名
            use_symlinks: 是否使用软链接，None时使用配置默认值

        Returns:
            Image: 创建的图片数据库记录

        Raises:
            ValueError: 如果图片格式无效
            FileExistsError: 如果图片已存在
        """
        # 验证图片格式
        if not self.validate_image_format(source_path):
            raise ValueError(f"不支持的图片格式或文件过大: {original_filename}")

        # 先计算哈希值检查重复，避免不必要的文件复制
        hash_value = self.calculate_image_hash(source_path)
        existing_image = self.check_duplicate_image(hash_value)
        if existing_image:
            # 如果图片已存在，直接返回现有记录，不抛出异常
            return existing_image

        # 获取图片信息
        width, height, file_size = self.get_image_info(source_path)

        # 组织目标路径
        target_path = self.organize_image_path(hash_value, original_filename)

        # 根据配置使用软链接或复制文件 - 使用线程安全的文件操作
        should_use_symlinks = (
            use_symlinks if use_symlinks is not None else settings.USE_SYMLINKS
        )

        from .file_operations import file_operation_manager

        success, error_msg = file_operation_manager.safe_copy_or_link(
            source_path, target_path, should_use_symlinks
        )

        if not success:
            raise ValueError(f"文件操作失败 {original_filename}: {error_msg}")

        # 计算相对路径（相对于storage根目录）
        relative_path = target_path.relative_to(settings.STORAGE_ROOT)

        # 创建数据库记录
        image = Image(
            hash_value=hash_value,
            file_path=str(relative_path),
            file_size=file_size,
            width=width,
            height=height,
        )

        self.db.add(image)
        self.db.commit()
        self.db.refresh(image)

        return image

    def get_image_by_id(self, image_id: int) -> Optional[Image]:
        """根据ID获取图片记录"""
        return self.db.query(Image).filter(Image.id == image_id).first()

    def get_image_by_hash(self, hash_value: str) -> Optional[Image]:
        """根据哈希值获取图片记录"""
        return self.db.query(Image).filter(Image.hash_value == hash_value).first()

    def get_absolute_path(self, image: Image) -> Path:
        """获取图片的绝对路径"""
        return (settings.STORAGE_ROOT / image.file_path).resolve()

    def get_storage_path(self, image: Image) -> Path:
        """获取图片在存储目录中的路径（不解析软链接）"""
        return settings.STORAGE_ROOT / image.file_path

    def delete_image(self, image_id: int) -> bool:
        """
        删除图片记录和文件

        Args:
            image_id: 图片ID

        Returns:
            bool: 是否成功删除
        """
        image = self.get_image_by_id(image_id)
        if not image:
            return False

        # 检查是否有关联的样本
        if image.samples:
            raise ValueError(
                f"无法删除图片：仍有 {len(image.samples)} 个样本使用此图片"
            )

        # 删除物理文件
        file_path = self.get_absolute_path(image)
        if file_path.exists():
            file_path.unlink()

        # 删除数据库记录
        self.db.delete(image)
        self.db.commit()

        return True

    def check_existing_images_batch(self, hash_values: list[str]) -> set[str]:
        """
        批量检查图片哈希值是否已存在

        Args:
            hash_values: 哈希值列表

        Returns:
            Set[str]: 已存在的哈希值集合
        """
        if not hash_values:
            return set()

        # 分块查询以避免SQL变量限制
        chunk_size = 500
        existing_hashes = set()

        for i in range(0, len(hash_values), chunk_size):
            chunk = hash_values[i : i + chunk_size]
            results = (
                self.db.query(Image.hash_value)
                .filter(Image.hash_value.in_(chunk))
                .all()
            )
            existing_hashes.update(result[0] for result in results)

        return existing_hashes

    def get_images_by_hashes_batch(self, hash_values: list[str]) -> dict[str, Image]:
        """
        批量获取图片记录

        Args:
            hash_values: 哈希值列表

        Returns:
            Dict[str, Image]: 哈希值到图片记录的映射
        """
        if not hash_values:
            return {}

        # 分块查询以避免SQL变量限制
        chunk_size = 500
        images_dict = {}

        for i in range(0, len(hash_values), chunk_size):
            chunk = hash_values[i : i + chunk_size]
            results = self.db.query(Image).filter(Image.hash_value.in_(chunk)).all()
            for image in results:
                images_dict[image.hash_value] = image

        return images_dict

    def bulk_create_images(
        self, image_data_list: list[dict], commit: bool = True, verbose: bool = False
    ) -> tuple[list[Image], list[str]]:
        """
        批量创建图片记录

        Args:
            image_data_list: 图片数据字典列表，每个字典包含:
                - hash_value: 哈希值
                - file_path: 文件路径
                - file_size: 文件大小
                - width: 图片宽度
                - height: 图片高度
            commit: 是否立即提交事务

        Returns:
            Tuple[List[Image], List[str]]: (成功创建的图片列表, 错误信息列表)
        """
        if not image_data_list:
            return [], []

        created_images = []
        errors = []

        try:
            # 检查重复的哈希值
            hash_values = [data["hash_value"] for data in image_data_list]
            existing_hashes = self.check_existing_images_batch(hash_values)

            # 过滤出需要创建的图片数据
            new_image_data = []
            skipped_existing = 0
            for data in image_data_list:
                if data["hash_value"] in existing_hashes:
                    skipped_existing += 1
                    # 不再将已存在的图片作为错误，而是静默跳过
                else:
                    new_image_data.append(data)

            # 根据verbose参数决定如何报告已存在的图片
            if skipped_existing > 0:
                if verbose:
                    # 详细模式：列出每个跳过的图片
                    for data in image_data_list:
                        if data["hash_value"] in existing_hashes:
                            errors.append(f"图片已存在: {data['hash_value']}")
                else:
                    # 简洁模式：只显示汇总信息
                    errors.append(f"跳过 {skipped_existing} 个已存在的图片（正常行为）")

            if new_image_data:
                # 创建Image对象列表
                image_objects = []
                for data in new_image_data:
                    image = Image(
                        hash_value=data["hash_value"],
                        file_path=data["file_path"],
                        file_size=data["file_size"],
                        width=data.get("width"),
                        height=data.get("height"),
                    )
                    image_objects.append(image)

                # 批量添加到会话
                self.db.add_all(image_objects)

                if commit:
                    self.db.commit()
                    # 刷新对象以获取ID
                    for image in image_objects:
                        self.db.refresh(image)
                else:
                    self.db.flush()  # 获取ID但不提交

                created_images = image_objects

        except Exception as e:
            if commit:
                self.db.rollback()
            errors.append(f"批量创建图片失败: {str(e)}")

        return created_images, errors
