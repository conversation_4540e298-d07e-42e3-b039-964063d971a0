"""
Thread-safe file operations for Ruyi Dataverse

This module provides thread-safe file operations to prevent race conditions
during parallel import operations when multiple workers try to access the same files.
"""

import shutil
import threading
import time
from pathlib import Path
from typing import Dict, Optional


class FileOperationManager:
    """Manager for thread-safe file operations"""

    def __init__(self):
        # Global locks for file operations - keyed by target path
        self._file_locks: Dict[str, threading.Lock] = {}
        self._locks_lock = threading.Lock()

    def _get_file_lock(self, file_path: Path) -> threading.Lock:
        """Get or create a lock for a specific file path"""
        path_str = str(file_path.resolve())

        with self._locks_lock:
            if path_str not in self._file_locks:
                self._file_locks[path_str] = threading.Lock()
            return self._file_locks[path_str]

    def safe_copy_or_link(
        self,
        source_path: Path,
        target_path: Path,
        use_symlinks: bool,
        max_retries: int = 3,
    ) -> tuple[bool, Optional[str]]:
        """
        Thread-safe file copy or link operation

        Args:
            source_path: Source file path
            target_path: Target file path
            use_symlinks: Whether to use symlinks instead of copying
            max_retries: Maximum number of retry attempts

        Returns:
            Tuple of (success: bool, error_message: Optional[str])
        """
        # Use file-specific lock to prevent concurrent operations on same target
        file_lock = self._get_file_lock(target_path)

        for attempt in range(max_retries):
            try:
                with file_lock:
                    # Double-check if file exists after acquiring lock
                    if target_path.exists():
                        # Verify file integrity if it exists
                        if self._verify_file_exists_and_valid(target_path):
                            return True, None
                        else:
                            # File exists but is invalid, remove it
                            target_path.unlink()

                    # Ensure target directory exists
                    target_path.parent.mkdir(parents=True, exist_ok=True)

                    if use_symlinks:
                        source_abs_path = source_path.resolve()
                        target_path.symlink_to(source_abs_path)
                    else:
                        shutil.copy2(source_path, target_path)

                    return True, None

            except OSError as e:
                error_msg = str(e).lower()

                # Handle specific error cases
                if "same file" in error_msg:
                    # Source and target are the same file - treat as success
                    return True, None
                elif "file exists" in error_msg:
                    # File was created by another thread - check if valid
                    if target_path.exists() and self._verify_file_exists_and_valid(
                        target_path
                    ):
                        return True, None
                elif attempt < max_retries - 1:
                    # Retry with exponential backoff for transient errors
                    wait_time = (2**attempt) * 0.1  # 0.1s, 0.2s, 0.4s
                    time.sleep(wait_time)
                    continue

                return False, f"文件操作失败: {str(e)}"

            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = (2**attempt) * 0.1
                    time.sleep(wait_time)
                    continue
                return False, f"文件操作异常: {str(e)}"

        return False, f"文件操作失败，已重试 {max_retries} 次"

    def _verify_file_exists_and_valid(self, file_path: Path) -> bool:
        """Verify that file exists and has non-zero size"""
        try:
            return file_path.exists() and file_path.stat().st_size > 0
        except (OSError, FileNotFoundError):
            return False

    def cleanup_locks(self):
        """Clean up unused locks (call periodically to prevent memory leaks)"""
        with self._locks_lock:
            # Remove locks for files that no longer exist
            paths_to_remove = []
            for path_str in self._file_locks:
                try:
                    if not Path(path_str).exists():
                        paths_to_remove.append(path_str)
                except (OSError, FileNotFoundError):
                    paths_to_remove.append(path_str)

            for path_str in paths_to_remove:
                del self._file_locks[path_str]


# Global instance for use across the application
file_operation_manager = FileOperationManager()
