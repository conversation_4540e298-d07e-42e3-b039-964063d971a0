"""
Parallel Import Service with PostgreSQL optimization
"""

import json
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple

from sqlalchemy.orm import Session

from ..models import Batch, Image, Sample
from ..schemas import BatchCreate, SampleCreate
from .batch_service import BatchService
from .image_service import ImageService
from .parallel_db_manager import parallel_db_manager
from .sample_service import SampleService


class ParallelImportService:
    """
    并行导入服务，针对PostgreSQL优化，其他数据库使用顺序处理
    """

    def __init__(self, db: Session):
        self.db = db
        self.image_service = ImageService(db)
        self.sample_service = SampleService(db)
        self.batch_service = BatchService(db)

    def import_batch_parallel(
        self,
        json_file_path: Path,
        image_base_path: Optional[Path] = None,
        image_progress_callback: Optional[Callable[[int, int], None]] = None,
        sample_progress_callback: Optional[Callable[[int, int], None]] = None,
        use_symlinks: Optional[bool] = None,
        chunk_size: int = 1000,
    ) -> Tu<PERSON>[Batch, List[Sample], List[str]]:
        """
        并行导入批次数据，针对PostgreSQL优化

        Args:
            json_file_path: JSON文件路径
            image_base_path: 图片基础路径
            image_progress_callback: 图片处理进度回调
            sample_progress_callback: 样本创建进度回调
            use_symlinks: 是否使用软链接
            chunk_size: 处理块大小

        Returns:
            Tuple: (批次记录, 样本列表, 错误信息列表)
        """
        # 解析JSON数据
        data = self._parse_import_json(json_file_path)

        if image_base_path is None:
            image_base_path = json_file_path.parent

        all_errors = []

        try:
            # 创建批次
            batch_data = BatchCreate(
                name=data["batch_name"], description=data.get("batch_description", "")
            )
            batch = self.batch_service.create_batch(batch_data)

            # 收集所有图片路径
            image_paths = [sample["image_path"] for sample in data["samples"]]

            # 并行处理图片
            processed_images, image_errors = self._process_images_parallel(
                image_base_path,
                image_paths,
                image_progress_callback,
                use_symlinks,
                chunk_size,
            )
            all_errors.extend(image_errors)

            # 并行创建样本
            created_samples, sample_errors = self._create_samples_parallel(
                data["samples"],
                processed_images,
                batch.id,
                sample_progress_callback,
            )
            all_errors.extend(sample_errors)

            # 如果没有任何样本成功创建，删除空的批次
            if not created_samples:
                self.batch_service.delete_batch(batch.id)
                raise ValueError(
                    f"导入失败：没有样本被成功创建。错误数量: {len(all_errors)}"
                )

            return batch, created_samples, all_errors

        except Exception as e:
            # 如果有严重错误，回滚事务
            self.db.rollback()
            raise ValueError(f"导入失败: {str(e)}")

    def _parse_import_json(self, json_file_path: Path) -> Dict[str, Any]:
        """解析导入的JSON文件"""
        try:
            with open(json_file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            return data
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON格式错误: {e}")
        except FileNotFoundError:
            raise ValueError(f"文件不存在: {json_file_path}")

    def _process_images_parallel(
        self,
        image_base_path: Path,
        image_paths: List[str],
        progress_callback: Optional[Callable[[int, int], None]] = None,
        use_symlinks: Optional[bool] = None,
        chunk_size: int = 1000,
    ) -> Tuple[Dict[str, Image], List[str]]:
        """并行处理图片文件"""

        if not parallel_db_manager.is_parallel_supported():
            # 回退到原有的map-reduce实现
            from .import_service import ImportService

            import_service = ImportService(self.db)
            return import_service.process_image_batch_mapreduce(
                image_base_path,
                image_paths,
                progress_callback,
                use_symlinks,
                chunk_size,
            )

        # PostgreSQL并行处理 - 使用分块避免内存和连接问题
        # 对于大数据集，仍然需要分块处理以避免资源耗尽
        total_images = len(image_paths)
        processed_images = {}
        all_errors = []

        # 分块处理图片
        for i in range(0, total_images, chunk_size):
            chunk_paths = image_paths[i : i + chunk_size]
            chunk_start = i

            # 处理当前块的文件操作
            chunk_image_progress_callback: Optional[Callable[[int, int], None]] = None
            if progress_callback:

                def _chunk_image_progress_callback(current: int, _total: int) -> None:
                    progress_callback(chunk_start + current, total_images)

                chunk_image_progress_callback = _chunk_image_progress_callback

            file_results = self._process_files_parallel(
                chunk_paths,
                image_base_path,
                use_symlinks,
                chunk_image_progress_callback,
            )

            # 处理当前块的结果
            chunk_image_data_list = []
            chunk_path_to_hash = {}

            for path, image_info, error in file_results:
                if error:
                    all_errors.append(error)
                elif image_info:
                    chunk_image_data_list.append(image_info)
                    chunk_path_to_hash[path] = image_info["hash_value"]

            # 并行创建当前块的图片记录
            if chunk_image_data_list:
                created_images, image_errors = self._create_images_parallel(
                    chunk_image_data_list
                )
                all_errors.extend(image_errors)

                # 构建路径到图片对象的映射
                for path, hash_value in chunk_path_to_hash.items():
                    if hash_value in created_images:
                        processed_images[path] = created_images[hash_value]

        return processed_images, all_errors

    def _process_files_parallel(
        self,
        image_paths: List[str],
        image_base_path: Path,
        use_symlinks: Optional[bool],
        progress_callback: Optional[Callable[[int, int], None]] = None,
    ) -> List[Tuple[str, Optional[Dict], Optional[str]]]:
        """并行处理文件操作（复制、哈希计算等）"""
        from concurrent.futures import ThreadPoolExecutor, as_completed

        from ..config import settings

        def process_single_file(image_path: str):
            try:
                # 重用现有的文件处理逻辑
                # 这里调用单个文件处理的逻辑
                # 注意：这个方法需要从ImportService中提取出来
                return self._process_single_image_file(
                    image_path, image_base_path, use_symlinks
                )
            except Exception as e:
                return image_path, None, f"文件处理错误 {image_path}: {str(e)}"

        # 并行处理文件
        max_workers = min(settings.IMPORT_MAX_WORKERS, len(image_paths))
        results = []
        completed_count = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_path = {
                executor.submit(process_single_file, path): path for path in image_paths
            }

            for future in as_completed(future_to_path):
                results.append(future.result())
                completed_count += 1

                # 更新进度
                if progress_callback:
                    progress_callback(completed_count, len(image_paths))

        return results

    def _process_single_image_file(
        self, image_path: str, image_base_path: Path, use_symlinks: Optional[bool]
    ) -> Tuple[str, Optional[Dict], Optional[str]]:
        """处理单个图片文件"""
        try:
            import hashlib

            from PIL import Image as PILImage

            from ..config import settings

            # 构建完整路径
            full_path = image_base_path / image_path
            if not full_path.exists():
                return image_path, None, f"图片文件不存在: {full_path}"

            # 计算文件哈希
            hash_md5 = hashlib.md5()
            with open(full_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            file_hash = hash_md5.hexdigest()

            # 获取图片尺寸
            try:
                with PILImage.open(full_path) as img:
                    width, height = img.size
            except Exception:
                width, height = None, None

            # 确定存储路径
            storage_path = settings.IMAGES_DIR / f"{file_hash}{full_path.suffix}"

            # 复制或链接文件 - 使用线程安全的文件操作
            from .file_operations import file_operation_manager

            should_use_symlinks = use_symlinks if use_symlinks is not None else False
            success, error_msg = file_operation_manager.safe_copy_or_link(
                full_path, storage_path, should_use_symlinks
            )

            if not success:
                return image_path, None, error_msg

            return (
                image_path,
                {
                    "hash_value": file_hash,
                    "file_path": str(storage_path.relative_to(settings.STORAGE_ROOT)),
                    "file_size": full_path.stat().st_size,
                    "width": width,
                    "height": height,
                },
                None,
            )

        except Exception as e:
            return image_path, None, f"处理图片失败 {image_path}: {str(e)}"

    def _create_images_parallel(
        self, image_data_list: List[Dict]
    ) -> Tuple[Dict[str, Image], List[str]]:
        """并行创建图片记录"""

        def create_image_worker(image_data: Dict, session: Session) -> Dict:
            from sqlalchemy.dialects.postgresql import insert

            from .retry_utils import is_retryable_db_error

            # Use retry logic for database operations
            for retry_attempt in range(3):  # Max 3 attempts
                try:
                    # Use PostgreSQL INSERT ON CONFLICT for atomic upsert
                    stmt = insert(Image).values(
                        hash_value=image_data["hash_value"],
                        file_path=image_data["file_path"],
                        file_size=image_data["file_size"],
                        width=image_data["width"],
                        height=image_data["height"],
                    )

                    # On conflict with hash_value, do nothing (use existing record)
                    stmt = stmt.on_conflict_do_nothing(index_elements=["hash_value"])

                    result = session.execute(stmt)
                    session.commit()

                    # Get the actual record (either newly created or existing)
                    image = (
                        session.query(Image)
                        .filter(Image.hash_value == image_data["hash_value"])
                        .first()
                    )

                    if not image:
                        raise RuntimeError(
                            f"Failed to retrieve image with hash {image_data['hash_value']}"
                        )

                    return {
                        "hash": image_data["hash_value"],
                        "image": image,
                        "created": result.rowcount > 0,
                    }

                except Exception as e:
                    session.rollback()

                    # Check if we should retry
                    if retry_attempt < 2 and is_retryable_db_error(e):
                        import random
                        import time

                        # Exponential backoff with jitter
                        delay = (2**retry_attempt) * 0.1 + random.uniform(0, 0.05)
                        time.sleep(delay)
                        continue
                    else:
                        # Final attempt failed or non-retryable error
                        return {"hash": image_data["hash_value"], "error": str(e)}

            # This should never be reached
            return {"hash": image_data["hash_value"], "error": "Max retries exceeded"}

        # 执行并行任务 - 使用更少的工作线程避免连接池耗尽和减少竞争条件
        results = parallel_db_manager.execute_parallel(
            image_data_list, create_image_worker, max_workers=2
        )

        # 处理结果
        created_images = {}
        errors = []

        for result in results:
            if "error" in result:
                error_msg = result["error"]
                # Provide more specific error messages for common issues
                if "unique constraint" in error_msg.lower():
                    errors.append(
                        f"图片哈希冲突（已存在） {result['hash']}: 这通常是正常的重复检测"
                    )
                elif "failed to retrieve" in error_msg.lower():
                    errors.append(f"图片检索失败 {result['hash']}: {error_msg}")
                else:
                    errors.append(f"图片创建失败 {result['hash']}: {error_msg}")
            else:
                created_images[result["hash"]] = result["image"]

        return created_images, errors

    def _create_samples_parallel(
        self,
        samples_data: List[Dict],
        processed_images: Dict[str, Image],
        batch_id: int,
        progress_callback: Optional[Callable[[int, int], None]] = None,
    ) -> Tuple[List[Sample], List[str]]:
        """并行创建样本记录"""

        if not parallel_db_manager.is_parallel_supported():
            # 回退到顺序处理
            return self._create_samples_sequential(
                samples_data, processed_images, batch_id, progress_callback
            )

        # PostgreSQL并行处理 - 使用分块避免内存和连接问题
        all_errors = []
        created_samples = []

        # 准备所有样本创建任务
        sample_tasks = []
        for sample_data in samples_data:
            image_path = sample_data["image_path"]
            if image_path in processed_images:
                sample_tasks.append({
                    "sample_data": sample_data,
                    "image": processed_images[image_path],
                    "batch_id": batch_id,
                })
            else:
                all_errors.append(f"图片未找到: {image_path}")

        def create_sample_worker(task: Dict, session: Session) -> Dict:
            try:
                sample_service = SampleService(session)

                # 创建样本数据对象
                # 处理JSON中的"metadata"字段映射到"sample_metadata"
                sample_metadata = task["sample_data"].get(
                    "metadata", task["sample_data"].get("sample_metadata", {})
                )

                sample_create = SampleCreate(
                    image_id=task["image"].id,
                    batch_id=task["batch_id"],
                    mode=task["sample_data"]["mode"],
                    sample_metadata=sample_metadata,
                    labels=task["sample_data"].get("labels", []),
                )

                # 创建样本
                sample = sample_service.create_sample(sample_create)
                return {"sample": sample, "success": True}

            except Exception as e:
                session.rollback()
                # 添加详细的错误信息用于调试
                image = task.get("image")
                image_id = image.id if image else "unknown"
                mode = task.get("sample_data", {}).get("mode", "unknown")
                error_msg = f"Sample creation failed: {str(e)} | Image ID: {image_id} | Mode: {mode}"
                return {"error": error_msg, "success": False}

        # 分块处理样本创建，避免一次性处理过多样本
        chunk_size = 500  # 每次处理500个样本
        total_tasks = len(sample_tasks)

        for i in range(0, total_tasks, chunk_size):
            chunk_tasks = sample_tasks[i : i + chunk_size]
            chunk_start = i

            if not chunk_tasks:
                continue

            # 处理当前块的样本
            chunk_progress_callback: Optional[Callable[[int, int], None]] = None
            if progress_callback:

                def _chunk_progress_callback(current: int, _total: int) -> None:
                    overall_current = chunk_start + current
                    progress_callback(overall_current, total_tasks)

                chunk_progress_callback = _chunk_progress_callback

            results = parallel_db_manager.execute_parallel(
                chunk_tasks,
                create_sample_worker,
                max_workers=2,  # 减少样本创建的并行度
                progress_callback=chunk_progress_callback,
            )

            # 处理当前块的结果
            for result in results:
                # 检查结果格式 - parallel_db_manager可能返回不同的格式
                if isinstance(result, dict):
                    if result.get("success", False):
                        created_samples.append(result["sample"])
                    else:
                        # 处理错误 - 可能来自worker函数或parallel_db_manager
                        error_msg = result.get(
                            "error", f"Unknown error in result: {result}"
                        )
                        all_errors.append(error_msg)
                        # 打印前几个错误用于调试
                        if len(all_errors) <= 3:
                            print(
                                f"DEBUG - Sample creation error {len(all_errors)}: {error_msg}"
                            )
                else:
                    # 意外的结果格式
                    error_msg = f"Unexpected result format: {result}"
                    all_errors.append(error_msg)
                    if len(all_errors) <= 3:
                        print(
                            f"DEBUG - Unexpected result {len(all_errors)}: {error_msg}"
                        )

        return created_samples, all_errors

    def _create_samples_sequential(
        self,
        samples_data: List[Dict],
        processed_images: Dict[str, Image],
        batch_id: int,
        progress_callback: Optional[Callable[[int, int], None]] = None,
    ) -> Tuple[List[Sample], List[str]]:
        """顺序创建样本（回退方案）"""
        # 使用原有的批量创建逻辑
        # 转换为SampleCreate对象
        sample_creates = []
        errors = []

        for sample_data in samples_data:
            image_path = sample_data["image_path"]
            if image_path in processed_images:
                # 处理JSON中的"metadata"字段映射到"sample_metadata"
                sample_metadata = sample_data.get(
                    "metadata", sample_data.get("sample_metadata", {})
                )

                sample_create = SampleCreate(
                    image_id=processed_images[image_path].id,
                    batch_id=batch_id,
                    mode=sample_data["mode"],
                    sample_metadata=sample_metadata,
                    labels=sample_data.get("labels", []),
                )
                sample_creates.append(sample_create)
            else:
                errors.append(f"图片未找到: {image_path}")

        # 使用现有的批量创建方法
        created_samples, create_errors = self.sample_service.create_samples_batch(
            sample_creates, progress_callback
        )
        errors.extend(create_errors)

        return created_samples, errors
