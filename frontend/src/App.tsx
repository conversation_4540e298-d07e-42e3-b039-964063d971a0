import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { AdminAuthProvider } from './contexts/AdminAuthContext';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import AdminProtectedRoute from './components/AdminProtectedRoute';
import AdminPathHandler from './components/AdminPathHandler';
import Login from './pages/Login';
import Sessions from './pages/Sessions.tsx';
import AnnotationWorkspace from './pages/AnnotationWorkspace.tsx';
import AdminLogin from './pages/AdminLogin';
import AdminDashboard from './pages/AdminDashboard';
import AdminSessionDetail from './pages/AdminSessionDetail';
import { getRouterBasename } from './utils/config';

function App() {
  const basename = getRouterBasename();

  return (
    <AuthProvider>
      <AdminAuthProvider>
        <Router basename={basename}>
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<Login />} />

            {/* Admin routes */}
            <Route path="/:dashboardPath/login" element={<AdminLogin />} />
            <Route path="/:dashboardPath/dashboard" element={
              <AdminProtectedRoute>
                <AdminDashboard />
              </AdminProtectedRoute>
            } />
            <Route path="/:dashboardPath/sessions/:sessionId" element={
              <AdminProtectedRoute>
                <AdminSessionDetail />
              </AdminProtectedRoute>
            } />
            {/* Protected annotation routes with layout */}
            <Route path="/sessions" element={
              <ProtectedRoute>
                <Layout>
                  <Sessions />
                </Layout>
              </ProtectedRoute>
            } />
            <Route path="/sessions/:sessionId/annotate" element={
              <ProtectedRoute>
                <Layout>
                  <AnnotationWorkspace />
                </Layout>
              </ProtectedRoute>
            } />
            <Route path="/" element={<Navigate to="/sessions" replace />} />

            {/* Handle bare dashboard paths - check if it's a valid admin path */}
            <Route path="/:dashboardPath" element={<AdminPathHandler />} />
          </Routes>
        </Router>
      </AdminAuthProvider>
    </AuthProvider>
  );
}

export default App;
