{"name": "ruyidv-annotation", "private": true, "version": "0.2.4", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:path": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "4.1.11", "@types/react-router-dom": "5.3.3", "axios": "1.10.0", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "react": "19.1.0", "react-dom": "19.1.0", "react-i18next": "^15.6.0", "react-router-dom": "7.6.3"}, "devDependencies": {"@eslint/js": "9.30.1", "@types/node": "^24.0.13", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@vitejs/plugin-react": "4.6.0", "autoprefixer": "10.4.21", "eslint": "9.30.1", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.20", "globals": "16.3.0", "postcss": "8.5.6", "tailwindcss": "4.1.11", "typescript": "5.8.3", "typescript-eslint": "8.35.1", "vite": "7.0.0"}}