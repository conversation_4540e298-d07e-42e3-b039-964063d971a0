[project]
name = "ruyidv"
version = "0.2.4"
description = "Ruyi Dataverse: Training data management for Ruyi Grounding Model"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic>=1.16.2",
    "bcrypt>=4.3.0",
    "click>=8.2.1",
    "fastapi>=0.115.14",
    "importlib>=1.0.4",
    "jinja2>=3.1.0",
    "pillow>=11.3.0",
    "psycopg2-binary>=2.9.0",
    "pydantic>=2.11.7",
    "python-jose[cryptography]>=3.5.0",
    "pyyaml>=6.0.0",
    "rich>=14.0.0",
    "sqlalchemy>=2.0.41",
    "tomli>=2.2.1",
    "uvicorn>=0.35.0",
]

[dependency-groups]
dev = [
    "ipykernel>=6.29.5",
    "orjson>=3.10.18",
    "pandas>=2.3.0",
    "poethepoet>=0.36.0",
    "pyarrow>=20.0.0",
    "pyright>=1.1.402",
    "ruff>=0.12.1",
    "tqdm>=4.67.1",
]

[project.scripts]
ruyidv = "ruyidv.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.ruff]
target-version = "py312"

[tool.ruff.lint]
preview = true

[tool.ruff.format]
preview = true

[tool.poe.tasks]
_ruff_check_format = "ruff format . --check --preview"
_ruff_check = "ruff check . --preview"
_pyright = "pyright"
_ruff_sort_imports = "ruff check --select I --fix . --preview"
_ruff_format_code = "ruff format . --preview"
fix = "ruff check --fix . --preview"
fix_unsafe = "ruff check --preview --fix --unsafe-fixes ."
export = "uv pip compile pyproject.toml -o requirements.txt"
bump = "./scripts/sync_version.sh sync"

[[tool.poe.tasks.format]]
sequence = ['_ruff_sort_imports', '_ruff_format_code']
ignore_fail = 'return_non_zero'

[[tool.poe.tasks.check]]
sequence = ['_ruff_check_format', '_ruff_check', '_pyright']
ignore_fail = 'return_non_zero'

[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/"
default = true
