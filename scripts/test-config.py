#!/usr/bin/env python3
"""
Test script to verify frontend path prefix configuration
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from ruyidv.config import settings

def test_configuration():
    """Test the current configuration"""
    print("🔧 Ruyi Dataverse Configuration Test")
    print("=" * 40)
    
    print(f"Frontend Path Prefix: {settings.FRONTEND_PATH_PREFIX}")
    print(f"Serve Frontend: {settings.SERVE_FRONTEND}")
    print(f"CORS Origins: {settings.CORS_ORIGINS}")
    print(f"API V1 Prefix: {settings.API_V1_PREFIX}")
    print(f"Frontend Static Dir: {settings.FRONTEND_STATIC_DIR}")
    
    # Calculate the combined API prefix
    frontend_prefix = settings.FRONTEND_PATH_PREFIX.rstrip('/') if settings.FRONTEND_PATH_PREFIX != '/' else ''
    api_prefix = f"{frontend_prefix}{settings.API_V1_PREFIX}"
    
    print(f"\nCalculated API Prefix: {api_prefix}")
    
    # Test different scenarios
    print("\n🧪 Test Scenarios:")
    test_cases = [
        ("/", "/api/v1"),
        ("/anno/", "/anno/api/v1"),
        ("/dataverse/", "/dataverse/api/v1"),
    ]
    
    for path_prefix, expected_api in test_cases:
        calculated_prefix = path_prefix.rstrip('/') if path_prefix != '/' else ''
        calculated_api = f"{calculated_prefix}{settings.API_V1_PREFIX}"
        status = "✅" if calculated_api == expected_api else "❌"
        print(f"  {status} Path: {path_prefix} → API: {calculated_api} (expected: {expected_api})")
    
    print(f"\n📁 Static Directory Exists: {settings.FRONTEND_STATIC_DIR.exists()}")
    if settings.FRONTEND_STATIC_DIR.exists():
        print(f"   Index.html Exists: {(settings.FRONTEND_STATIC_DIR / 'index.html').exists()}")
        print(f"   Assets Dir Exists: {(settings.FRONTEND_STATIC_DIR / 'assets').exists()}")

if __name__ == "__main__":
    test_configuration()
