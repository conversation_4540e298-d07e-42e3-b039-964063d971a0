#!/bin/bash

# Example deployment script for Ruyi Dataverse with path prefix
# This script demonstrates how to deploy the application with a custom path prefix

set -e

# Configuration
PATH_PREFIX="/anno/"
API_BASE_URL=""  # Empty for relative URLs
HOST="0.0.0.0"
PORT="8000"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Ruyi Dataverse Deployment Example${NC}"
echo -e "${BLUE}====================================${NC}"
echo -e "Path Prefix: ${GREEN}${PATH_PREFIX}${NC}"
echo -e "Host: ${GREEN}${HOST}:${PORT}${NC}"
echo ""

# Step 1: Configure backend
echo -e "${YELLOW}📝 Step 1: Configuring backend...${NC}"
uv run ruyidv config frontend --path-prefix="$PATH_PREFIX"

# Step 2: Build frontend
echo -e "${YELLOW}🔨 Step 2: Building frontend...${NC}"
./scripts/build-frontend.sh "$PATH_PREFIX" "$API_BASE_URL"

# Step 3: Show final configuration
echo -e "${YELLOW}📋 Step 3: Final configuration${NC}"
echo ""
echo -e "${BLUE}Backend configuration:${NC}"
uv run ruyidv config show | grep -A 10 "frontend:" || echo "Frontend config not found"

echo ""
echo -e "${GREEN}✅ Deployment preparation complete!${NC}"
echo ""
echo -e "${YELLOW}🚀 To start the server:${NC}"
echo -e "   ${GREEN}uv run ruyidv serve --host ${HOST} --port ${PORT}${NC}"
echo ""
echo -e "${YELLOW}🌐 Access the application at:${NC}"
echo -e "   ${GREEN}http://${HOST}:${PORT}${PATH_PREFIX}${NC}"
echo ""
echo -e "${YELLOW}📚 For more deployment options, see:${NC}"
echo -e "   ${GREEN}doc/frontend_deployment.md${NC}"
