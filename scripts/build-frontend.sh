#!/bin/bash

# Frontend build script with path prefix support
# Usage: ./scripts/build-frontend.sh [path_prefix] [api_base_url]

set -e

# Default values
DEFAULT_PATH_PREFIX="/"
DEFAULT_API_BASE_URL=""

# Parse arguments
PATH_PREFIX="${1:-$DEFAULT_PATH_PREFIX}"
API_BASE_URL="${2:-$DEFAULT_API_BASE_URL}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Building Ruyi Dataverse Frontend${NC}"
echo -e "${BLUE}=================================${NC}"
echo -e "Path Prefix: ${GREEN}${PATH_PREFIX}${NC}"
echo -e "API Base URL: ${GREEN}${API_BASE_URL:-'(relative)'}${NC}"
echo ""

# Check if we're in the right directory
if [ ! -f "frontend/package.json" ]; then
    echo -e "${RED}❌ Error: frontend/package.json not found${NC}"
    echo -e "${RED}Please run this script from the project root directory${NC}"
    exit 1
fi

# Change to frontend directory
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 Installing dependencies...${NC}"
    npm install
fi

# Set environment variables for build
export VITE_PATH_PREFIX="$PATH_PREFIX"
export VITE_API_BASE_URL="$API_BASE_URL"

echo -e "${YELLOW}🔨 Building frontend...${NC}"

# Build the frontend
npm run build

echo -e "${GREEN}✅ Frontend build completed successfully!${NC}"
echo ""
echo -e "${BLUE}📁 Build output location: ${GREEN}frontend/dist/${NC}"
echo -e "${BLUE}🌐 Deployment path: ${GREEN}${PATH_PREFIX}${NC}"

# Show build summary
if [ -d "dist" ]; then
    echo ""
    echo -e "${BLUE}📊 Build Summary:${NC}"
    echo -e "$(find dist -type f -name "*.js" -o -name "*.css" -o -name "*.html" | wc -l) files generated"
    
    # Show main files
    echo -e "${BLUE}Main files:${NC}"
    ls -la dist/ | grep -E '\.(html|js|css)$' || true
fi

echo ""
echo -e "${GREEN}🎉 Ready for deployment!${NC}"

# Show deployment instructions
echo ""
echo -e "${YELLOW}💡 Deployment Instructions:${NC}"
echo -e "1. Configure backend path prefix:"
echo -e "   ${GREEN}uv run ruyidv config frontend --path-prefix='${PATH_PREFIX}'${NC}"
echo ""
echo -e "2. Start the backend server:"
echo -e "   ${GREEN}uv run ruyidv serve${NC}"
echo ""
echo -e "3. Access the application at:"
if [ "$PATH_PREFIX" = "/" ]; then
    echo -e "   ${GREEN}http://localhost:8000/${NC}"
else
    echo -e "   ${GREEN}http://localhost:8000${PATH_PREFIX}${NC}"
fi
